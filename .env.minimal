# ============================================================================
# ⚡ MINIMAL REQUIRED ENVIRONMENT VARIABLES
# ============================================================================

# 🤖 OpenAI API (REQUIRED - you have this)
OPENAI_API_KEY=your_openai_api_key_here

# 📧 Email Configuration (REQUIRED)
EMAIL=<EMAIL>
CLAIMS_EMAIL_PASSWORD=aagoahfjyttmmowm

# 🎫 Zendesk Configuration (REQUIRED)
ZENDESK_SUBDOMAIN=your_zendesk_subdomain
ZENDESK_EMAIL=<EMAIL>
ZENDESK_API_TOKEN=your_zendesk_api_token

# ��️ Supabase Configuration (REQUIRED)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# 🤝 HumanLayer Configuration (REQUIRED)
HUMANLAYER_API_KEY=your_humanlayer_api_key
HUMANLAYER_WEBHOOK_SECRET=your_humanlayer_webhook_secret

# ============================================================================
# 📝 INSTRUCTIONS:
# 1. Copy this to .env: cp .env.minimal .env
# 2. Replace "your_*" values with your actual API keys
# 3. Save this file
# 4. Run: docker-compose up
# ============================================================================
