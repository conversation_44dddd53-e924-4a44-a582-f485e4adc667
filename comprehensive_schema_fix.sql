-- 🔧 COMPREHENSIVE DATABASE SCHEMA FIX
-- This script aligns ALL tables with the application code expectations
-- Run this script in your Supabase SQL Editor

-- ========================================
-- 1. FIX CLAIMS TABLE
-- ========================================

-- Add missing columns to claims table (ALL NULLABLE)
ALTER TABLE claims
ADD COLUMN IF NOT EXISTS email_subject TEXT NULL,
ADD COLUMN IF NOT EXISTS email_body TEXT NULL,
ADD COLUMN IF NOT EXISTS sender_name TEXT NULL,
ADD COLUMN IF NOT EXISTS received_at TIMESTAMP WITH TIME ZONE NULL,
ADD COLUMN IF NOT EXISTS confidence_level TEXT NULL,
ADD COLUMN IF NOT EXISTS ai_classification_result JSONB NULL,
ADD COLUMN IF NOT EXISTS consensus_confidence DECIMAL(5,4) NULL,
ADD COLUMN IF NOT EXISTS policy_number TEXT NULL,
ADD COLUMN IF NOT EXISTS incident_date TIMESTAMP WITH TIME ZONE NULL,
ADD COLUMN IF NOT EXISTS incident_location TEXT NULL,
ADD COLUMN IF NOT EXISTS estimated_value DECIMAL(12,2) NULL,
ADD COLUMN IF NOT EXISTS claimant_name TEXT NULL,
ADD COLUMN IF NOT EXISTS claimant_phone TEXT NULL,
ADD COLUMN IF NOT EXISTS zendesk_ticket_id TEXT NULL,
ADD COLUMN IF NOT EXISTS zendesk_ticket_url TEXT NULL,
ADD COLUMN IF NOT EXISTS priority_score INTEGER NULL,
ADD COLUMN IF NOT EXISTS zendesk_status TEXT NULL,
ADD COLUMN IF NOT EXISTS attachments_count INTEGER NULL;

-- Migrate existing data from old column names to new ones
UPDATE claims 
SET 
    email_subject = COALESCE(email_subject, subject),
    email_body = COALESCE(email_body, body),
    confidence_level = COALESCE(confidence_level, confidence::text)
WHERE email_subject IS NULL OR email_body IS NULL OR confidence_level IS NULL;

-- ========================================
-- 2. FIX ZENDESK_TICKETS TABLE  
-- ========================================

-- Add missing columns to zendesk_tickets table (ALL NULLABLE)
ALTER TABLE zendesk_tickets
ADD COLUMN IF NOT EXISTS description TEXT NULL,
ADD COLUMN IF NOT EXISTS assignee_id TEXT NULL,
ADD COLUMN IF NOT EXISTS assignee_email TEXT NULL,
ADD COLUMN IF NOT EXISTS group_id TEXT NULL,
ADD COLUMN IF NOT EXISTS ai_priority_score INTEGER NULL,
ADD COLUMN IF NOT EXISTS complexity_level TEXT NULL,
ADD COLUMN IF NOT EXISTS estimated_resolution_hours INTEGER NULL,
ADD COLUMN IF NOT EXISTS sync_error TEXT NULL;

-- ========================================
-- 3. ATTACHMENTS TABLE IS ALREADY CORRECT
-- ========================================
-- The attachments table already has all required fields from your schema

-- ========================================
-- 4. FIX CLAIM_HISTORY TABLE
-- ========================================

-- Add missing columns to claim_history table (ALL NULLABLE)
ALTER TABLE claim_history
ADD COLUMN IF NOT EXISTS event_description TEXT NULL,
ADD COLUMN IF NOT EXISTS triggered_by TEXT NULL,
ADD COLUMN IF NOT EXISTS user_id TEXT NULL,
ADD COLUMN IF NOT EXISTS processing_step TEXT NULL,
ADD COLUMN IF NOT EXISTS context_metadata JSONB NULL,
ADD COLUMN IF NOT EXISTS error_details JSONB NULL;

-- Migrate existing data
UPDATE claim_history 
SET 
    event_description = COALESCE(event_description, description),
    triggered_by = COALESCE(triggered_by, 'system'),
    context_metadata = COALESCE(context_metadata, metadata)
WHERE event_description IS NULL OR triggered_by IS NULL;

-- ========================================
-- 5. CREATE MISSING INDEXES FOR PERFORMANCE
-- ========================================

-- Claims table indexes
CREATE INDEX IF NOT EXISTS idx_claims_email_subject ON claims(email_subject);
CREATE INDEX IF NOT EXISTS idx_claims_sender_name ON claims(sender_name);
CREATE INDEX IF NOT EXISTS idx_claims_claim_type ON claims(claim_type);
CREATE INDEX IF NOT EXISTS idx_claims_urgency_level ON claims(urgency_level);
CREATE INDEX IF NOT EXISTS idx_claims_confidence_level ON claims(confidence_level);
CREATE INDEX IF NOT EXISTS idx_claims_policy_number ON claims(policy_number);
CREATE INDEX IF NOT EXISTS idx_claims_claimant_name ON claims(claimant_name);
CREATE INDEX IF NOT EXISTS idx_claims_zendesk_ticket_id ON claims(zendesk_ticket_id);
CREATE INDEX IF NOT EXISTS idx_claims_received_at ON claims(received_at);

-- Zendesk tickets table indexes
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_assignee_id ON zendesk_tickets(assignee_id);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_group_id ON zendesk_tickets(group_id);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_ai_priority_score ON zendesk_tickets(ai_priority_score);

-- Attachments table indexes (additional)
CREATE INDEX IF NOT EXISTS idx_attachments_filename ON attachments(filename);
CREATE INDEX IF NOT EXISTS idx_attachments_status ON attachments(status);
CREATE INDEX IF NOT EXISTS idx_attachments_document_type ON attachments(document_type);
CREATE INDEX IF NOT EXISTS idx_attachments_uploaded_at ON attachments(uploaded_at);

-- Claim history table indexes
CREATE INDEX IF NOT EXISTS idx_claim_history_event_type ON claim_history(event_type);
CREATE INDEX IF NOT EXISTS idx_claim_history_triggered_by ON claim_history(triggered_by);
CREATE INDEX IF NOT EXISTS idx_claim_history_processing_step ON claim_history(processing_step);

-- ========================================
-- 6. VERIFY SCHEMA ALIGNMENT
-- ========================================

-- Show all table schemas to verify alignment
SELECT 'CLAIMS TABLE SCHEMA:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'claims' AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT 'ZENDESK_TICKETS TABLE SCHEMA:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'zendesk_tickets' AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT 'ATTACHMENTS TABLE SCHEMA:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'attachments' AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT 'CLAIM_HISTORY TABLE SCHEMA:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'claim_history' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Show record counts
SELECT 
    'claims' as table_name, 
    count(*) as record_count 
FROM claims
UNION ALL
SELECT 
    'zendesk_tickets' as table_name, 
    count(*) as record_count 
FROM zendesk_tickets
UNION ALL
SELECT 
    'attachments' as table_name, 
    count(*) as record_count 
FROM attachments
UNION ALL
SELECT 
    'claim_history' as table_name, 
    count(*) as record_count 
FROM claim_history;

-- ========================================
-- 7. SUCCESS MESSAGE
-- ========================================
SELECT '✅ COMPREHENSIVE SCHEMA FIX COMPLETED SUCCESSFULLY!' as status;
SELECT 'All tables now align with application code expectations.' as message;
SELECT 'All new columns are nullable to prevent insertion errors.' as note;
