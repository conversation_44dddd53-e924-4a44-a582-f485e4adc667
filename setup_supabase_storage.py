#!/usr/bin/env python3
"""
🗄️ Supabase Storage Bucket Setup

This script creates the required storage bucket for claims attachments.
Run this after setting up the database schema.
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

def setup_storage_bucket():
    """Create storage bucket using Supabase client"""
    print("🗄️ Setting up Supabase Storage Bucket...")
    
    try:
        from src.config.settings import Settings
        from supabase import create_client
        
        # Initialize Supabase client directly
        settings = Settings()
        supabase = create_client(settings.supabase_url, settings.supabase_service_role_key)
        
        bucket_name = "claims-attachments"
        
        # Check if bucket exists
        print(f"\n1️⃣ Checking if bucket '{bucket_name}' exists...")
        try:
            buckets = supabase.storage.list_buckets()
            existing_bucket = next((b for b in buckets if b.name == bucket_name), None)
            
            if existing_bucket:
                print(f"✅ Bucket '{bucket_name}' already exists")
            else:
                print(f"📁 Creating bucket '{bucket_name}'...")
                
                # Create bucket
                result = supabase.storage.create_bucket(
                    bucket_name,
                    options={
                        "public": False,  # Private bucket
                        "file_size_limit": 52428800,  # 50MB limit
                        "allowed_mime_types": [
                            "image/jpeg", "image/png", "image/gif", "image/webp",
                            "application/pdf",
                            "application/msword",
                            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                            "application/vnd.ms-excel",
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            "text/plain", "text/csv"
                        ]
                    }
                )
                
                if result:
                    print(f"✅ Bucket '{bucket_name}' created successfully")
                else:
                    print(f"❌ Failed to create bucket '{bucket_name}'")
                    
        except Exception as e:
            print(f"❌ Error with bucket operations: {str(e)}")
            
        # Test bucket access
        print(f"\n2️⃣ Testing bucket access...")
        try:
            files = supabase.storage.from_(bucket_name).list()
            print(f"✅ Bucket access successful - {len(files)} files found")
        except Exception as e:
            print(f"❌ Bucket access failed: {str(e)}")
            
        # Create a test file
        print(f"\n3️⃣ Testing file upload...")
        try:
            test_content = f"Test file created at {os.popen('date').read().strip()}"
            test_path = "test/setup_test.txt"
            
            result = supabase.storage.from_(bucket_name).upload(
                test_path,
                test_content.encode(),
                file_options={"content-type": "text/plain"}
            )
            
            if result:
                print(f"✅ Test file uploaded successfully: {test_path}")
                
                # Test download URL
                download_url = supabase.storage.from_(bucket_name).create_signed_url(
                    test_path, 3600  # 1 hour expiry
                )
                
                if download_url:
                    print(f"✅ Download URL generated: {download_url['signedURL'][:50]}...")
                else:
                    print("❌ Failed to generate download URL")
                    
            else:
                print("❌ Test file upload failed")
                
        except Exception as e:
            print(f"❌ File upload test failed: {str(e)}")
            
        print(f"\n✅ Storage setup completed!")
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        print("Make sure you have the supabase-py package installed")
    except Exception as e:
        print(f"❌ Setup failed: {str(e)}")

if __name__ == "__main__":
    setup_storage_bucket()
