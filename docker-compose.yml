# 🐳 Docker Compose for Zurich AI Claims Processing System
version: '3.8'

services:
  # Main application service
  zurich-claims-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: zurich-claims-api
    ports:
      - "8000:8000"
    environment:
      # Application settings
      - APP_NAME=Zurich AI Claims Processing
      - APP_VERSION=1.0.0
      - APP_DEBUG=false
      - APP_ENVIRONMENT=docker
      
      # API Configuration
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_WORKERS=4
      - API_RELOAD=false
      
      # AI Model API Keys (set these in .env file)
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      
      # Email Configuration (REQUIRED)
      - EMAIL=${EMAIL}
      - CLAIMS_EMAIL_PASSWORD=${CLAIMS_EMAIL_PASSWORD}
      
      # HumanLayer Configuration
      - HUMANLAYER_API_KEY=${HUMANLAYER_API_KEY:-}
      - HUMANLAYER_WEBHOOK_SECRET=${HUMANLAYER_WEBHOOK_SECRET:-}

      # Zendesk Configuration (REQUIRED)
      - ZENDESK_SUBDOMAIN=${ZENDESK_SUBDOMAIN}
      - ZENDESK_EMAIL=${ZENDESK_EMAIL}
      - ZENDESK_API_TOKEN=${ZENDESK_API_TOKEN}

      # Supabase Configuration (REQUIRED)
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      
      # Logging
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      
      # Performance
      - MAX_WORKERS=10
      - TIMEOUT_SECONDS=300
      
    volumes:
      # Mount logs directory
      - ./logs:/app/logs
      # Mount config for development (optional)
      - ./src/config:/app/src/config:ro
    
    networks:
      - zurich-network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    depends_on:
      - redis
    
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.zurich-api.rule=Host(`localhost`)"
      - "traefik.http.services.zurich-api.loadbalancer.server.port=8000"

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: zurich-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - zurich-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

# Networks
networks:
  zurich-network:
    driver: bridge
    name: zurich-claims-network

# Volumes
volumes:
  redis_data:
    name: zurich-redis-data
