#!/usr/bin/env python3
"""
🧪 Simple Supabase Database and Storage Test

This script tests:
1. Database connectivity and write operations
2. Storage bucket operations
3. Basic CRUD operations on claims table
4. File upload and download from storage
"""

import asyncio
import os
import sys
import json
import uuid
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from src.config.settings import Settings
from src.database.supabase_client import SupabaseClient
from src.utils.dozzle_logger import dozzle_log

async def test_database_operations():
    """Test basic database operations"""
    print("\n🗄️  Testing Database Operations...")
    
    try:
        # Initialize settings and client
        settings = Settings()
        supabase_client = SupabaseClient(settings)
        
        # Test 1: Create a test claim
        print("\n1️⃣  Testing claim creation...")
        test_claim_id = str(uuid.uuid4())
        
        claim_data = {
            'id': test_claim_id,
            'workflow_id': f'test_workflow_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'sender_email': '<EMAIL>',
            'subject': 'Test Claim Submission',
            'body': 'This is a test claim for database verification',
            'email_type': 'CLAIM_SUBMISSION',
            'confidence': 0.95,
            'claim_type': 'AUTO',
            'urgency_level': 'MEDIUM',
            'status': 'PENDING',
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }
        
        result = await supabase_client.create_claim(claim_data)
        print(f"✅ Claim created successfully: {result.get('id', 'Unknown ID')}")
        
        # Test 2: Read the claim back
        print("\n2️⃣  Testing claim retrieval...")
        retrieved_claim = await supabase_client.get_claim(test_claim_id)
        if retrieved_claim:
            print(f"✅ Claim retrieved successfully: {retrieved_claim.get('subject', 'No subject')}")
        else:
            print("❌ Failed to retrieve claim")
            
        # Test 3: Update the claim
        print("\n3️⃣  Testing claim update...")
        update_data = {
            'status': 'PROCESSED',
            'updated_at': datetime.utcnow().isoformat()
        }
        updated_claim = await supabase_client.update_claim(test_claim_id, update_data)
        print(f"✅ Claim updated successfully: Status = {updated_claim.get('status', 'Unknown')}")
        
        # Test 4: Create Zendesk ticket record
        print("\n4️⃣  Testing Zendesk ticket record creation...")
        ticket_data = {
            'claim_id': test_claim_id,
            'zendesk_ticket_id': '12345',
            'zendesk_url': 'https://test.zendesk.com/agent/tickets/12345',
            'subject': 'Test Ticket',
            'status': 'new',
            'priority': 'normal',
            'sync_status': 'synced'
        }
        
        ticket_record = await supabase_client.create_zendesk_ticket_record(ticket_data)
        print(f"✅ Zendesk ticket record created: {ticket_record.get('id', 'Unknown ID')}")
        
        # Test 5: Add claim history
        print("\n5️⃣  Testing claim history...")
        await supabase_client.add_claim_history(
            claim_id=test_claim_id,
            event_type="test_event",
            description="Test history entry",
            new_values={'test': 'value'},
            metadata={'test_run': True}
        )
        print("✅ Claim history added successfully")
        
        print("\n✅ All database tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Database test failed: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        return False

async def test_storage_operations():
    """Test storage bucket operations"""
    print("\n📁 Testing Storage Operations...")
    
    try:
        # Initialize settings and client
        settings = Settings()
        supabase_client = SupabaseClient(settings)
        
        # Test 1: Create a test file
        print("\n1️⃣  Creating test file...")
        test_content = f"Test file content created at {datetime.now().isoformat()}"
        test_filename = f"test_file_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(test_filename, 'w') as f:
            f.write(test_content)
        
        print(f"✅ Test file created: {test_filename}")
        
        # Test 2: Upload file to storage
        print("\n2️⃣  Testing file upload...")
        workflow_id = f"test_workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        upload_result = await supabase_client.upload_attachment(
            file_path=test_filename,
            workflow_id=workflow_id,
            original_filename=test_filename
        )
        
        if upload_result:
            print(f"✅ File uploaded successfully: {upload_result.get('storage_path', 'Unknown path')}")
            storage_path = upload_result.get('storage_path')
            
            # Test 3: Generate download URL
            print("\n3️⃣  Testing download URL generation...")
            download_url = await supabase_client.get_attachment_download_url(storage_path)
            if download_url:
                print(f"✅ Download URL generated: {download_url[:50]}...")
            else:
                print("❌ Failed to generate download URL")
                
        else:
            print("❌ File upload failed")
            
        # Cleanup: Remove test file
        if os.path.exists(test_filename):
            os.remove(test_filename)
            print(f"🧹 Cleaned up test file: {test_filename}")
            
        print("\n✅ All storage tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Storage test failed: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        return False

async def test_environment_variables():
    """Test that all required environment variables are available"""
    print("\n🔧 Testing Environment Variables...")
    
    required_vars = [
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY', 
        'SUPABASE_SERVICE_ROLE_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * 10}{value[-4:] if len(value) > 4 else '****'}")
        else:
            print(f"❌ {var}: Missing")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("\n✅ All environment variables present!")
        return True

async def main():
    """Run all Supabase tests"""
    print("🧪 Starting Supabase Simple Test Suite")
    print("=" * 50)
    
    # Test environment variables first
    env_ok = await test_environment_variables()
    if not env_ok:
        print("\n❌ Environment variables test failed. Please check your .env file.")
        return
    
    # Test database operations
    db_ok = await test_database_operations()
    
    # Test storage operations
    storage_ok = await test_storage_operations()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"Environment Variables: {'✅ PASS' if env_ok else '❌ FAIL'}")
    print(f"Database Operations: {'✅ PASS' if db_ok else '❌ FAIL'}")
    print(f"Storage Operations: {'✅ PASS' if storage_ok else '❌ FAIL'}")
    
    if all([env_ok, db_ok, storage_ok]):
        print("\n🎉 All tests passed! Supabase is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
