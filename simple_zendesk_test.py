#!/usr/bin/env python3
"""
Simple Zendesk Direct HTTP API Test
Tests authentication and ticket creation without dependencies
"""

import os
import requests
import base64
import json
from dotenv import load_dotenv

def test_zendesk_direct_api():
    """Test Zendesk Direct HTTP API"""
    print("🧪 Testing Zendesk Direct HTTP API")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Get credentials from environment
    subdomain = os.getenv('ZENDESK_SUBDOMAIN')
    email = os.getenv('ZENDESK_EMAIL')
    token = os.getenv('ZENDESK_API_TOKEN')
    
    print(f"📋 Configuration:")
    print(f"   Subdomain: {subdomain}")
    print(f"   Email: {email}")
    print(f"   Token length: {len(token) if token else 0}")
    
    if not all([subdomain, email, token]):
        print("❌ Missing required environment variables")
        return False
    
    # Create authentication header
    auth_string = f"{email}/token:{token}"
    auth_bytes = base64.b64encode(auth_string.encode()).decode()
    
    headers = {
        "Authorization": f"Basic {auth_bytes}",
        "Content-Type": "application/json",
        "Accept": "application/json",
        "User-Agent": "Zurich-Claims-Processing/1.0"
    }
    
    base_url = f"https://{subdomain}.zendesk.com/api/v2"
    
    # Test 1: Authentication
    print(f"\n🔍 Test 1: Authentication (/users/me)")
    print("-" * 30)
    
    try:
        auth_url = f"{base_url}/users/me.json"
        print(f"📡 Making request to: {auth_url}")
        
        response = requests.get(auth_url, headers=headers, timeout=30)
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            user_data = response.json()
            user = user_data.get('user', {})
            print(f"✅ Authentication successful!")
            print(f"   User ID: {user.get('id')}")
            print(f"   Name: {user.get('name')}")
            print(f"   Email: {user.get('email')}")
            print(f"   Role: {user.get('role')}")
            print(f"   Active: {user.get('active')}")
        else:
            print(f"❌ Authentication failed")
            print(f"   Response: {response.text[:300]}")
            return False
            
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return False
    
    # Test 2: Create Mock Ticket
    print(f"\n🎫 Test 2: Create Mock Ticket")
    print("-" * 30)
    
    try:
        ticket_url = f"{base_url}/tickets.json"
        print(f"📡 Making request to: {ticket_url}")
        
        # Prepare ticket payload
        ticket_payload = {
            "ticket": {
                "subject": "🧪 Test Ticket - Direct HTTP API Verification",
                "comment": {
                    "body": "This is a test ticket created using direct HTTP API calls to verify Zendesk connectivity. This ticket can be safely deleted.",
                    "public": False
                },
                "priority": "low",
                "type": "question",
                "status": "new",
                "requester": {
                    "email": "<EMAIL>",
                    "name": "API Test User"
                },
                "tags": ["test", "api-verification", "direct-http", "zurich-claims"]
            }
        }
        
        print(f"📝 Ticket payload:")
        print(f"   Subject: {ticket_payload['ticket']['subject']}")
        print(f"   Priority: {ticket_payload['ticket']['priority']}")
        print(f"   Type: {ticket_payload['ticket']['type']}")
        
        response = requests.post(
            ticket_url, 
            json=ticket_payload, 
            headers=headers, 
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            ticket = result.get('ticket', {})
            
            print(f"✅ Ticket created successfully!")
            print(f"   Ticket ID: {ticket.get('id')}")
            print(f"   Ticket URL: {ticket.get('url')}")
            print(f"   Status: {ticket.get('status')}")
            print(f"   Created: {ticket.get('created_at')}")
            print(f"   Subject: {ticket.get('subject')}")
            
            return {
                'ticket_id': ticket.get('id'),
                'ticket_url': ticket.get('url'),
                'status': ticket.get('status'),
                'created_at': ticket.get('created_at')
            }
        else:
            print(f"❌ Ticket creation failed")
            print(f"   Response: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Ticket creation error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Simple Zendesk Direct HTTP API Test")
    print("=" * 60)
    
    result = test_zendesk_direct_api()
    
    if result:
        print(f"\n🎉 All tests passed successfully!")
        print(f"   ✅ Authentication working")
        print(f"   ✅ Ticket creation working")
        print(f"\n🔗 Created ticket URL: {result.get('ticket_url')}")
        print(f"\n💡 The direct HTTP API implementation is ready to replace zenpy!")
    else:
        print(f"\n❌ Tests failed. Please check your credentials and network connection.")

if __name__ == "__main__":
    main()
