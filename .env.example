# 🔑 Zurich Claims AI - Environment Variables Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# 🤖 AI MODEL API KEYS
# =============================================================================

# OpenAI GPT-4o (Primary reasoning model)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude (Backup model)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Hugging Face (For Legal-BERT, RiskBERT, FinBERT)
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# =============================================================================
# 🔍 OCR SERVICE API KEYS
# =============================================================================

# Azure Document Intelligence
AZURE_DOCUMENT_INTELLIGENCE_KEY=your_azure_key_here
AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=your_azure_endpoint_here

# Google Cloud Vision API
GOOGLE_CLOUD_VISION_API_KEY=your_google_cloud_key_here
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account.json

# AWS Textract
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# =============================================================================
# 🤝 HUMANLAYER CONFIGURATION
# =============================================================================

# HumanLayer API Key (from https://app.humanlayer.dev)
HUMANLAYER_API_KEY=your_humanlayer_api_key_here
HUMANLAYER_WEBHOOK_SECRET=your_webhook_secret_here

# Email addresses for human approvals
CLAIMS_MANAGER_EMAIL=<EMAIL>
COMPLIANCE_EMAIL=<EMAIL>
UNDERWRITING_EMAIL=<EMAIL>
LEGAL_EMAIL=<EMAIL>

# =============================================================================
# 📧 EMAIL CONFIGURATION
# =============================================================================

# Gmail Configuration (for monitoring and sending)
GMAIL_EMAIL=<EMAIL>
GMAIL_APP_PASSWORD=your_gmail_app_password_here

# SMTP Configuration (for professional emails)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=your_smtp_username
SMTP_PASSWORD=your_smtp_password

# =============================================================================
# 🎫 ZENDESK INTEGRATION
# =============================================================================

# Zendesk API Configuration
ZENDESK_URL=https://your-company.zendesk.com
ZENDESK_EMAIL=<EMAIL>
ZENDESK_TOKEN=your_zendesk_api_token_here

# =============================================================================
# 🏛️ POLICY API CONFIGURATION
# =============================================================================

# Insurance Policy API (replace with your actual policy system)
POLICY_API_URL=https://your-policy-api.com
POLICY_API_KEY=your_policy_api_key_here

# =============================================================================
# 🗄️ DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
DATABASE_URL=postgresql://username:password@localhost:5432/zurich_claims
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=zurich_claims
DATABASE_USER=your_db_user
DATABASE_PASSWORD=your_db_password

# Redis (for caching and session management)
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# =============================================================================
# 🌐 WEB APPLICATION CONFIGURATION
# =============================================================================

# FastAPI Application
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=true
APP_SECRET_KEY=your_secret_key_for_sessions

# Webhook Configuration
WEBHOOK_BASE_URL=https://your-domain.com
WEBHOOK_SECRET=your_webhook_secret_here

# =============================================================================
# 📊 MONITORING & LOGGING
# =============================================================================

# Sentry (Error tracking)
SENTRY_DSN=your_sentry_dsn_here

# Prometheus (Metrics)
PROMETHEUS_PORT=9090

# Log Level
LOG_LEVEL=INFO

# =============================================================================
# 🔒 SECURITY CONFIGURATION
# =============================================================================

# Encryption keys
ENCRYPTION_KEY=your_32_character_encryption_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://your-frontend-domain.com

# =============================================================================
# 🇨🇦 CANADIAN LEGAL FRAMEWORK
# =============================================================================

# Canadian Legal Database API (if available)
CANADIAN_LEGAL_API_KEY=your_legal_api_key_here
CANADIAN_LEGAL_API_URL=https://your-legal-api.com

# Provincial Law Database
PROVINCIAL_LAW_DB_URL=your_provincial_law_db_url

# =============================================================================
# 🎯 WORKFLOW CONFIGURATION
# =============================================================================

# Processing timeouts (in minutes)
EMAIL_PROCESSING_TIMEOUT=30
OCR_PROCESSING_TIMEOUT=120
HUMAN_APPROVAL_TIMEOUT=240

# Claim value thresholds
HIGH_VALUE_CLAIM_THRESHOLD=100000
CRITICAL_CLAIM_THRESHOLD=500000

# =============================================================================
# 🧪 TESTING CONFIGURATION
# =============================================================================

# Test Environment
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/test_zurich_claims
TEST_REDIS_URL=redis://localhost:6379/1

# Test API Keys (use sandbox/test keys)
TEST_ANTHROPIC_API_KEY=your_test_anthropic_key
TEST_OPENAI_API_KEY=your_test_openai_key

# =============================================================================
# 📈 PERFORMANCE CONFIGURATION
# =============================================================================

# Concurrency limits
MAX_CONCURRENT_OCR_JOBS=5
MAX_CONCURRENT_AI_ANALYSIS=3
MAX_EMAIL_BATCH_SIZE=10

# Cache TTL (in seconds)
CACHE_TTL_SHORT=300    # 5 minutes
CACHE_TTL_MEDIUM=3600  # 1 hour
CACHE_TTL_LONG=86400   # 24 hours
