###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import baml_py
from enum import Enum

from pydantic import BaseModel, ConfigDict

from typing_extensions import TypeAlia<PERSON>, Literal
from typing import Dict, Generic, List, Optional, TypeVar, Union


T = TypeVar('T')
CheckName = TypeVar('CheckName', bound=str)

class Check(BaseModel):
    name: str
    expression: str
    status: str
class Checked(BaseModel, Generic[T,CheckName]):
    value: T
    checks: Dict[CheckName, Check]

def get_checks(checks: Dict[CheckName, Check]) -> List[Check]:
    return list(checks.values())

def all_succeeded(checks: Dict[CheckName, Check]) -> bool:
    return all(check.status == "succeeded" for check in get_checks(checks))



class ClaimDecision(str, Enum):
    
    APPROVE_PAYMENT = "APPROVE_PAYMENT"
    DENY_CLAIM = "DENY_CLAIM"
    REQUIRES_INVESTIGATION = "REQUIRES_INVESTIGATION"
    REQUIRES_HUMAN_APPROVAL = "REQUIRES_HUMAN_APPROVAL"
    REQUEST_MORE_INFORMATION = "REQUEST_MORE_INFORMATION"

class ClaimType(str, Enum):
    
    AUTO_ACCIDENT = "AUTO_ACCIDENT"
    PROPERTY_DAMAGE = "PROPERTY_DAMAGE"
    LIABILITY = "LIABILITY"
    PERSONAL_INJURY = "PERSONAL_INJURY"
    THEFT = "THEFT"
    FIRE_DAMAGE = "FIRE_DAMAGE"
    WATER_DAMAGE = "WATER_DAMAGE"
    VANDALISM = "VANDALISM"
    OTHER = "OTHER"

class ConfidenceLevel(str, Enum):
    
    VERY_LOW = "VERY_LOW"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    VERY_HIGH = "VERY_HIGH"

class CoverageStatus(str, Enum):
    
    COVERED = "COVERED"
    NOT_COVERED = "NOT_COVERED"
    PARTIALLY_COVERED = "PARTIALLY_COVERED"
    REQUIRES_INVESTIGATION = "REQUIRES_INVESTIGATION"
    POLICY_NOT_FOUND = "POLICY_NOT_FOUND"

class DocumentType(str, Enum):
    
    POLICE_REPORT = "POLICE_REPORT"
    MEDICAL_REPORT = "MEDICAL_REPORT"
    REPAIR_ESTIMATE = "REPAIR_ESTIMATE"
    PHOTO_EVIDENCE = "PHOTO_EVIDENCE"
    INSURANCE_FORM = "INSURANCE_FORM"
    RECEIPT = "RECEIPT"
    INVOICE = "INVOICE"
    WITNESS_STATEMENT = "WITNESS_STATEMENT"
    CORRESPONDENCE = "CORRESPONDENCE"
    OTHER_DOCUMENT = "OTHER_DOCUMENT"

class EmailType(str, Enum):
    
    CLAIM_SUBMISSION = "CLAIM_SUBMISSION"
    CLAIM_INQUIRY = "CLAIM_INQUIRY"
    POLICY_QUESTION = "POLICY_QUESTION"
    BILLING_INQUIRY = "BILLING_INQUIRY"
    GENERAL_INQUIRY = "GENERAL_INQUIRY"
    SPAM = "SPAM"
    NOT_INSURANCE_RELATED = "NOT_INSURANCE_RELATED"

class LegalRisk(str, Enum):
    
    VERY_LOW = "VERY_LOW"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    VERY_HIGH = "VERY_HIGH"

class LiabilityAssessment(str, Enum):
    
    CLEAR_LIABILITY = "CLEAR_LIABILITY"
    DISPUTED_LIABILITY = "DISPUTED_LIABILITY"
    NO_LIABILITY = "NO_LIABILITY"
    REQUIRES_INVESTIGATION = "REQUIRES_INVESTIGATION"

class NextAction(str, Enum):
    
    CREATE_ZENDESK_TICKET = "CREATE_ZENDESK_TICKET"
    SCHEDULE_INSPECTION = "SCHEDULE_INSPECTION"
    REQUEST_DOCUMENTS = "REQUEST_DOCUMENTS"
    CONTACT_CUSTOMER = "CONTACT_CUSTOMER"
    ESCALATE_TO_MANAGER = "ESCALATE_TO_MANAGER"
    CLOSE_INQUIRY = "CLOSE_INQUIRY"
    TRANSFER_TO_SPECIALIST = "TRANSFER_TO_SPECIALIST"

class UrgencyLevel(str, Enum):
    
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class ClaimRecommendation(BaseModel):
    decision: "ClaimDecision"
    next_actions: List["NextAction"]
    priority_level: "UrgencyLevel"
    target_response_time: int
    requires_human_approval: bool
    approval_reason: Optional[str] = None
    recommended_approver: Optional[str] = None
    customer_communication_required: bool
    communication_template: Optional[str] = None
    zendesk_priority: str
    zendesk_tags: List[str]
    recommendation_summary: str

class DocumentAnalysis(BaseModel):
    document_type: "DocumentType"
    confidence: "ConfidenceLevel"
    key_information: List[str]
    dates_mentioned: List[str]
    amounts_mentioned: List[float]
    is_readable: bool
    quality_score: int
    is_relevant_to_claim: bool
    relevance_explanation: str

class EmailAnalysis(BaseModel):
    email_type: "EmailType"
    is_claim: bool
    claim_type: Optional["ClaimType"] = None
    urgency_level: "UrgencyLevel"
    confidence: "ConfidenceLevel"
    policy_number: Optional[str] = None
    claim_number: Optional[str] = None
    incident_date: Optional[str] = None
    location: Optional[str] = None
    customer_name: Optional[str] = None
    customer_phone: Optional[str] = None
    customer_email: str
    summary: str
    key_details: List[str]
    attachments_mentioned: bool
    requires_human_review: bool
    requires_immediate_action: bool
    reasoning: str

class FinancialAssessment(BaseModel):
    estimated_claim_value: float
    confidence_range_min: float
    confidence_range_max: float
    property_damage_estimate: Optional[float] = None
    medical_costs_estimate: Optional[float] = None
    legal_costs_estimate: Optional[float] = None
    other_costs_estimate: Optional[float] = None
    exceeds_authority_limit: bool
    requires_reserves: bool
    financial_notes: str

class LegalAnalysis(BaseModel):
    legal_risk: "LegalRisk"
    liability_assessment: "LiabilityAssessment"
    applicable_province: Optional[str] = None
    relevant_statutes: List[str]
    precedent_cases: List[str]
    risk_factors: List[str]
    mitigation_strategies: List[str]
    legal_notes: str

class ModelConsensus(BaseModel):
    claude_analysis: "EmailAnalysis"
    gpt4_analysis: "EmailAnalysis"
    consensus_reached: bool
    consensus_confidence: "ConfidenceLevel"
    disagreement_areas: List[str]
    tie_breaker_needed: bool
    final_analysis: "EmailAnalysis"
    consensus_notes: str

class PolicyVerification(BaseModel):
    policy_found: bool
    policy_active: bool
    coverage_status: "CoverageStatus"
    policy_type: Optional[str] = None
    coverage_limits: Optional[float] = None
    deductible: Optional[float] = None
    verification_notes: str
    exclusions_apply: bool
    exclusion_details: List[str]
