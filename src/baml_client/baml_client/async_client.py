###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
from typing import Dict, List, Optional, TypeVar, Union, cast
from typing_extensions import Literal

import baml_py

from . import _baml
from ._baml import BamlCallOptions
from .types import Checked, Check
from .parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>treamParser
from .async_request import AsyncHttpRequest, AsyncHttpStreamRequest
from .globals import DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME


OutputType = TypeVar('OutputType')


class BamlAsyncClient:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager
    __stream_client: "BamlStreamClient"
    __http_request: AsyncHttpRequest
    __http_stream_request: AsyncHttpStreamRequest
    __llm_response_parser: LlmResponseParser
    __llm_stream_parser: LlmStreamParser
    __baml_options: _baml.BamlCallOptions

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager, baml_options: Optional[_baml.BamlCallOptions] = None):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager
      self.__stream_client = BamlStreamClient(self.__runtime, self.__ctx_manager, baml_options)
      self.__http_request = AsyncHttpRequest(self.__runtime, self.__ctx_manager)
      self.__http_stream_request = AsyncHttpStreamRequest(self.__runtime, self.__ctx_manager)
      self.__llm_response_parser = LlmResponseParser(self.__runtime, self.__ctx_manager)
      self.__llm_stream_parser = LlmStreamParser(self.__runtime, self.__ctx_manager)
      self.__baml_options = baml_options or {}

    def with_options(
      self,
      tb: Optional[_baml.type_builder.TypeBuilder] = None,
      client_registry: Optional[baml_py.baml_py.ClientRegistry] = None,
      collector: Optional[Union[baml_py.baml_py.Collector, List[baml_py.baml_py.Collector]]] = None,
      env: Optional[Dict[str, Optional[str]]] = None,
    ) -> "BamlAsyncClient":
      """
      Returns a new instance of BamlAsyncClient with explicitly typed baml options
      for Python 3.8 compatibility.
      """
      new_options = self.__baml_options.copy()

      # Override if any keyword arguments were provided.
      if tb is not None:
          new_options["tb"] = tb
      if client_registry is not None:
          new_options["client_registry"] = client_registry
      if collector is not None:
          new_options["collector"] = collector
      if env is not None:
          new_options["env"] = env

      return BamlAsyncClient(self.__runtime, self.__ctx_manager, new_options)

    @property
    def stream(self):
      return self.__stream_client

    @property
    def request(self):
      return self.__http_request

    @property
    def stream_request(self):
      return self.__http_stream_request

    @property
    def parse(self):
      return self.__llm_response_parser

    @property
    def parse_stream(self):
      return self.__llm_stream_parser

    
    async def AnalyzeDocument(
        self,
        document_text: str,document_filename: str,claim_context: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.DocumentAnalysis:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}

      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = await self.__runtime.call_function(
        "AnalyzeDocument",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_context": claim_context,
        },
        self.__ctx_manager.clone_context(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.DocumentAnalysis, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    async def AssessLegalRisk(
        self,
        incident_description: str,location: str,parties_involved: str,claim_value: float,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.LegalAnalysis:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}

      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = await self.__runtime.call_function(
        "AssessLegalRisk",
        {
          "incident_description": incident_description,"location": location,"parties_involved": parties_involved,"claim_value": claim_value,
        },
        self.__ctx_manager.clone_context(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.LegalAnalysis, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    async def ClassifyEmail(
        self,
        email_subject: str,email_body: str,sender_email: str,attachments: List[str],
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.EmailAnalysis:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}

      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = await self.__runtime.call_function(
        "ClassifyEmail",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments,
        },
        self.__ctx_manager.clone_context(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.EmailAnalysis, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    async def ValidateEmailClassification(
        self,
        email_subject: str,email_body: str,sender_email: str,attachments: List[str],
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.EmailAnalysis:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}

      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = await self.__runtime.call_function(
        "ValidateEmailClassification",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments,
        },
        self.__ctx_manager.clone_context(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.EmailAnalysis, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    async def VerifyPolicyDetails(
        self,
        policy_number: str,incident_date: str,claim_type: str,policy_data: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.PolicyVerification:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}

      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = await self.__runtime.call_function(
        "VerifyPolicyDetails",
        {
          "policy_number": policy_number,"incident_date": incident_date,"claim_type": claim_type,"policy_data": policy_data,
        },
        self.__ctx_manager.clone_context(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.PolicyVerification, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    


class BamlStreamClient:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager
    __baml_options: _baml.BamlCallOptions
    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager, baml_options: Optional[_baml.BamlCallOptions] = None):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager
      self.__baml_options = baml_options or {}

    
    def AnalyzeDocument(
        self,
        document_text: str,document_filename: str,claim_context: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlStream[_baml.partial_types.DocumentAnalysis, _baml.types.DocumentAnalysis]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function(
        "AnalyzeDocument",
        {
          "document_text": document_text,
          "document_filename": document_filename,
          "claim_context": claim_context,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlStream[_baml.partial_types.DocumentAnalysis, _baml.types.DocumentAnalysis](
        raw,
        lambda x: cast(_baml.partial_types.DocumentAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.DocumentAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def AssessLegalRisk(
        self,
        incident_description: str,location: str,parties_involved: str,claim_value: float,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlStream[_baml.partial_types.LegalAnalysis, _baml.types.LegalAnalysis]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function(
        "AssessLegalRisk",
        {
          "incident_description": incident_description,
          "location": location,
          "parties_involved": parties_involved,
          "claim_value": claim_value,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlStream[_baml.partial_types.LegalAnalysis, _baml.types.LegalAnalysis](
        raw,
        lambda x: cast(_baml.partial_types.LegalAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.LegalAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def ClassifyEmail(
        self,
        email_subject: str,email_body: str,sender_email: str,attachments: List[str],
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlStream[_baml.partial_types.EmailAnalysis, _baml.types.EmailAnalysis]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function(
        "ClassifyEmail",
        {
          "email_subject": email_subject,
          "email_body": email_body,
          "sender_email": sender_email,
          "attachments": attachments,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlStream[_baml.partial_types.EmailAnalysis, _baml.types.EmailAnalysis](
        raw,
        lambda x: cast(_baml.partial_types.EmailAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.EmailAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def ValidateEmailClassification(
        self,
        email_subject: str,email_body: str,sender_email: str,attachments: List[str],
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlStream[_baml.partial_types.EmailAnalysis, _baml.types.EmailAnalysis]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function(
        "ValidateEmailClassification",
        {
          "email_subject": email_subject,
          "email_body": email_body,
          "sender_email": sender_email,
          "attachments": attachments,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlStream[_baml.partial_types.EmailAnalysis, _baml.types.EmailAnalysis](
        raw,
        lambda x: cast(_baml.partial_types.EmailAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.EmailAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def VerifyPolicyDetails(
        self,
        policy_number: str,incident_date: str,claim_type: str,policy_data: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlStream[_baml.partial_types.PolicyVerification, _baml.types.PolicyVerification]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function(
        "VerifyPolicyDetails",
        {
          "policy_number": policy_number,
          "incident_date": incident_date,
          "claim_type": claim_type,
          "policy_data": policy_data,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlStream[_baml.partial_types.PolicyVerification, _baml.types.PolicyVerification](
        raw,
        lambda x: cast(_baml.partial_types.PolicyVerification, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.PolicyVerification, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    


b = BamlAsyncClient(DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX)

__all__ = ["b", "BamlCallOptions"]