###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import baml_py
from enum import Enum

from pydantic import BaseModel, ConfigDict

from typing_extensions import TypeAlias, Literal
from typing import Dict, Generic, List, Optional, TypeVar, Union

from . import types
from .types import Checked, Check

###############################################################################
#
#  These types are used for streaming, for when an instance of a type
#  is still being built up and any of its fields is not yet fully available.
#
###############################################################################

T = TypeVar('T')
class StreamState(BaseModel, Generic[T]):
    value: T
    state: Literal["Pending", "Incomplete", "Complete"]


class ClaimRecommendation(BaseModel):
    decision: Optional[types.ClaimDecision] = None
    next_actions: List[types.NextAction]
    priority_level: Optional[types.UrgencyLevel] = None
    target_response_time: Optional[int] = None
    requires_human_approval: Optional[bool] = None
    approval_reason: Optional[str] = None
    recommended_approver: Optional[str] = None
    customer_communication_required: Optional[bool] = None
    communication_template: Optional[str] = None
    zendesk_priority: Optional[str] = None
    zendesk_tags: List[str]
    recommendation_summary: Optional[str] = None

class DocumentAnalysis(BaseModel):
    document_type: Optional[types.DocumentType] = None
    confidence: Optional[types.ConfidenceLevel] = None
    key_information: List[str]
    dates_mentioned: List[str]
    amounts_mentioned: List[float]
    is_readable: Optional[bool] = None
    quality_score: Optional[int] = None
    is_relevant_to_claim: Optional[bool] = None
    relevance_explanation: Optional[str] = None

class EmailAnalysis(BaseModel):
    email_type: Optional[types.EmailType] = None
    is_claim: Optional[bool] = None
    claim_type: Optional[types.ClaimType] = None
    urgency_level: Optional[types.UrgencyLevel] = None
    confidence: Optional[types.ConfidenceLevel] = None
    policy_number: Optional[str] = None
    claim_number: Optional[str] = None
    incident_date: Optional[str] = None
    location: Optional[str] = None
    customer_name: Optional[str] = None
    customer_phone: Optional[str] = None
    customer_email: Optional[str] = None
    summary: Optional[str] = None
    key_details: List[str]
    attachments_mentioned: Optional[bool] = None
    requires_human_review: Optional[bool] = None
    requires_immediate_action: Optional[bool] = None
    reasoning: Optional[str] = None

class FinancialAssessment(BaseModel):
    estimated_claim_value: Optional[float] = None
    confidence_range_min: Optional[float] = None
    confidence_range_max: Optional[float] = None
    property_damage_estimate: Optional[float] = None
    medical_costs_estimate: Optional[float] = None
    legal_costs_estimate: Optional[float] = None
    other_costs_estimate: Optional[float] = None
    exceeds_authority_limit: Optional[bool] = None
    requires_reserves: Optional[bool] = None
    financial_notes: Optional[str] = None

class LegalAnalysis(BaseModel):
    legal_risk: Optional[types.LegalRisk] = None
    liability_assessment: Optional[types.LiabilityAssessment] = None
    applicable_province: Optional[str] = None
    relevant_statutes: List[str]
    precedent_cases: List[str]
    risk_factors: List[str]
    mitigation_strategies: List[str]
    legal_notes: Optional[str] = None

class ModelConsensus(BaseModel):
    claude_analysis: Optional["EmailAnalysis"] = None
    gpt4_analysis: Optional["EmailAnalysis"] = None
    consensus_reached: Optional[bool] = None
    consensus_confidence: Optional[types.ConfidenceLevel] = None
    disagreement_areas: List[str]
    tie_breaker_needed: Optional[bool] = None
    final_analysis: Optional["EmailAnalysis"] = None
    consensus_notes: Optional[str] = None

class PolicyVerification(BaseModel):
    policy_found: Optional[bool] = None
    policy_active: Optional[bool] = None
    coverage_status: Optional[types.CoverageStatus] = None
    policy_type: Optional[str] = None
    coverage_limits: Optional[float] = None
    deductible: Optional[float] = None
    verification_notes: Optional[str] = None
    exclusions_apply: Optional[bool] = None
    exclusion_details: List[str]
