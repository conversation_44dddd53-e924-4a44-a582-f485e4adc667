###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off

"""Private internal Baml namespace and utilities."""

import os
from typing import TypedDict, Dict, Optional, Union, List
from typing_extensions import NotRequired

import baml_py

from . import types
from . import partial_types
from . import type_builder


class BamlCallOptions(TypedDict, total=False):
  """Additional parameters for Baml function calls."""
  tb: NotRequired[type_builder.TypeBuilder]
  client_registry: NotRequired[baml_py.baml_py.ClientRegistry]
  collector: NotRequired[Union[baml_py.baml_py.Collector, List[baml_py.baml_py.Collector]]]
  env: NotRequired[Dict[str, Optional[str]]]


class BamlCallOptionsModApi(TypedDict, total=False):
  """Additional parameters for modular API calls (doesn't take a collector)."""
  tb: NotRequired[type_builder.TypeBuilder]
  client_registry: NotRequired[baml_py.baml_py.ClientRegistry]
  env: NotRequired[Dict[str, Optional[str]]]


def env_vars_to_dict(overrides: Dict[str, Optional[str]]) -> Dict[str, str]:
  base = os.environ.copy()
  for k, v in overrides.items():
    if v is not None:
      base[k] = v
    else:
      base.pop(k, None)
  return base


__all__ = [
  "types",
  "partial_types",
  "type_builder",
]