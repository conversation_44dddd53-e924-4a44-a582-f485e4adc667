###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import typing
from baml_py.baml_py import FieldType, EnumValueBuilder, EnumBuilder, ClassBuilder
from baml_py.type_builder import <PERSON><PERSON><PERSON><PERSON> as _<PERSON><PERSON><PERSON>er, Class<PERSON>ropertyBuilder, ClassPropertyViewer, <PERSON>umV<PERSON>ueViewer
from .globals import DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME


class TypeBuilder(_TypeBuilder):
    def __init__(self):
        super().__init__(classes=set(
          ["ClaimRecommendation","DocumentAnalysis","EmailAnalysis","FinancialAssessment","LegalAnalysis","ModelConsensus","PolicyVerification",]
        ), enums=set(
          ["ClaimDecision","ClaimType","ConfidenceLevel","CoverageStatus","DocumentType","EmailType","LegalRisk","LiabilityAssessment","NextAction","UrgencyLevel",]
        ), runtime=DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME)


    @property
    def ClaimRecommendation(self) -> "ClaimRecommendationAst":
        return ClaimRecommendationAst(self)

    @property
    def DocumentAnalysis(self) -> "DocumentAnalysisAst":
        return DocumentAnalysisAst(self)

    @property
    def EmailAnalysis(self) -> "EmailAnalysisAst":
        return EmailAnalysisAst(self)

    @property
    def FinancialAssessment(self) -> "FinancialAssessmentAst":
        return FinancialAssessmentAst(self)

    @property
    def LegalAnalysis(self) -> "LegalAnalysisAst":
        return LegalAnalysisAst(self)

    @property
    def ModelConsensus(self) -> "ModelConsensusAst":
        return ModelConsensusAst(self)

    @property
    def PolicyVerification(self) -> "PolicyVerificationAst":
        return PolicyVerificationAst(self)





class ClaimRecommendationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ClaimRecommendation")
        self._properties: typing.Set[str] = set([ "decision",  "next_actions",  "priority_level",  "target_response_time",  "requires_human_approval",  "approval_reason",  "recommended_approver",  "customer_communication_required",  "communication_template",  "zendesk_priority",  "zendesk_tags",  "recommendation_summary", ])
        self._props = ClaimRecommendationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ClaimRecommendationProperties":
        return self._props


class ClaimRecommendationViewer(ClaimRecommendationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ClaimRecommendationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def decision(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decision"))

    @property
    def next_actions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("next_actions"))

    @property
    def priority_level(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("priority_level"))

    @property
    def target_response_time(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("target_response_time"))

    @property
    def requires_human_approval(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requires_human_approval"))

    @property
    def approval_reason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("approval_reason"))

    @property
    def recommended_approver(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommended_approver"))

    @property
    def customer_communication_required(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("customer_communication_required"))

    @property
    def communication_template(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("communication_template"))

    @property
    def zendesk_priority(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("zendesk_priority"))

    @property
    def zendesk_tags(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("zendesk_tags"))

    @property
    def recommendation_summary(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendation_summary"))

    

class DocumentAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DocumentAnalysis")
        self._properties: typing.Set[str] = set([ "document_type",  "confidence",  "key_information",  "dates_mentioned",  "amounts_mentioned",  "is_readable",  "quality_score",  "is_relevant_to_claim",  "relevance_explanation", ])
        self._props = DocumentAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DocumentAnalysisProperties":
        return self._props


class DocumentAnalysisViewer(DocumentAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DocumentAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def document_type(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("document_type"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def key_information(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("key_information"))

    @property
    def dates_mentioned(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dates_mentioned"))

    @property
    def amounts_mentioned(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("amounts_mentioned"))

    @property
    def is_readable(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("is_readable"))

    @property
    def quality_score(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("quality_score"))

    @property
    def is_relevant_to_claim(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("is_relevant_to_claim"))

    @property
    def relevance_explanation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("relevance_explanation"))

    

class EmailAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EmailAnalysis")
        self._properties: typing.Set[str] = set([ "email_type",  "is_claim",  "claim_type",  "urgency_level",  "confidence",  "policy_number",  "claim_number",  "incident_date",  "location",  "customer_name",  "customer_phone",  "customer_email",  "summary",  "key_details",  "attachments_mentioned",  "requires_human_review",  "requires_immediate_action",  "reasoning", ])
        self._props = EmailAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EmailAnalysisProperties":
        return self._props


class EmailAnalysisViewer(EmailAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EmailAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def email_type(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("email_type"))

    @property
    def is_claim(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("is_claim"))

    @property
    def claim_type(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claim_type"))

    @property
    def urgency_level(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("urgency_level"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def policy_number(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policy_number"))

    @property
    def claim_number(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claim_number"))

    @property
    def incident_date(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incident_date"))

    @property
    def location(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("location"))

    @property
    def customer_name(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("customer_name"))

    @property
    def customer_phone(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("customer_phone"))

    @property
    def customer_email(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("customer_email"))

    @property
    def summary(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("summary"))

    @property
    def key_details(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("key_details"))

    @property
    def attachments_mentioned(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachments_mentioned"))

    @property
    def requires_human_review(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requires_human_review"))

    @property
    def requires_immediate_action(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requires_immediate_action"))

    @property
    def reasoning(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reasoning"))

    

class FinancialAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FinancialAssessment")
        self._properties: typing.Set[str] = set([ "estimated_claim_value",  "confidence_range_min",  "confidence_range_max",  "property_damage_estimate",  "medical_costs_estimate",  "legal_costs_estimate",  "other_costs_estimate",  "exceeds_authority_limit",  "requires_reserves",  "financial_notes", ])
        self._props = FinancialAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FinancialAssessmentProperties":
        return self._props


class FinancialAssessmentViewer(FinancialAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FinancialAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def estimated_claim_value(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("estimated_claim_value"))

    @property
    def confidence_range_min(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence_range_min"))

    @property
    def confidence_range_max(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence_range_max"))

    @property
    def property_damage_estimate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("property_damage_estimate"))

    @property
    def medical_costs_estimate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medical_costs_estimate"))

    @property
    def legal_costs_estimate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legal_costs_estimate"))

    @property
    def other_costs_estimate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("other_costs_estimate"))

    @property
    def exceeds_authority_limit(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exceeds_authority_limit"))

    @property
    def requires_reserves(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requires_reserves"))

    @property
    def financial_notes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("financial_notes"))

    

class LegalAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("LegalAnalysis")
        self._properties: typing.Set[str] = set([ "legal_risk",  "liability_assessment",  "applicable_province",  "relevant_statutes",  "precedent_cases",  "risk_factors",  "mitigation_strategies",  "legal_notes", ])
        self._props = LegalAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "LegalAnalysisProperties":
        return self._props


class LegalAnalysisViewer(LegalAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class LegalAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def legal_risk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legal_risk"))

    @property
    def liability_assessment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("liability_assessment"))

    @property
    def applicable_province(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("applicable_province"))

    @property
    def relevant_statutes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("relevant_statutes"))

    @property
    def precedent_cases(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("precedent_cases"))

    @property
    def risk_factors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("risk_factors"))

    @property
    def mitigation_strategies(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mitigation_strategies"))

    @property
    def legal_notes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legal_notes"))

    

class ModelConsensusAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ModelConsensus")
        self._properties: typing.Set[str] = set([ "claude_analysis",  "gpt4_analysis",  "consensus_reached",  "consensus_confidence",  "disagreement_areas",  "tie_breaker_needed",  "final_analysis",  "consensus_notes", ])
        self._props = ModelConsensusProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ModelConsensusProperties":
        return self._props


class ModelConsensusViewer(ModelConsensusAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ModelConsensusProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claude_analysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claude_analysis"))

    @property
    def gpt4_analysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("gpt4_analysis"))

    @property
    def consensus_reached(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("consensus_reached"))

    @property
    def consensus_confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("consensus_confidence"))

    @property
    def disagreement_areas(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("disagreement_areas"))

    @property
    def tie_breaker_needed(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("tie_breaker_needed"))

    @property
    def final_analysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("final_analysis"))

    @property
    def consensus_notes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("consensus_notes"))

    

class PolicyVerificationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("PolicyVerification")
        self._properties: typing.Set[str] = set([ "policy_found",  "policy_active",  "coverage_status",  "policy_type",  "coverage_limits",  "deductible",  "verification_notes",  "exclusions_apply",  "exclusion_details", ])
        self._props = PolicyVerificationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "PolicyVerificationProperties":
        return self._props


class PolicyVerificationViewer(PolicyVerificationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class PolicyVerificationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def policy_found(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policy_found"))

    @property
    def policy_active(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policy_active"))

    @property
    def coverage_status(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverage_status"))

    @property
    def policy_type(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policy_type"))

    @property
    def coverage_limits(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverage_limits"))

    @property
    def deductible(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("deductible"))

    @property
    def verification_notes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("verification_notes"))

    @property
    def exclusions_apply(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exclusions_apply"))

    @property
    def exclusion_details(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exclusion_details"))

    



class ClaimDecisionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ClaimDecision")
        self._values: typing.Set[str] = set([ "APPROVE_PAYMENT",  "DENY_CLAIM",  "REQUIRES_INVESTIGATION",  "REQUIRES_HUMAN_APPROVAL",  "REQUEST_MORE_INFORMATION", ])
        self._vals = ClaimDecisionValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ClaimDecisionValues":
        return self._vals


class ClaimDecisionViewer(ClaimDecisionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ClaimDecisionValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def APPROVE_PAYMENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("APPROVE_PAYMENT"))
    

    @property
    def DENY_CLAIM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("DENY_CLAIM"))
    

    @property
    def REQUIRES_INVESTIGATION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REQUIRES_INVESTIGATION"))
    

    @property
    def REQUIRES_HUMAN_APPROVAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REQUIRES_HUMAN_APPROVAL"))
    

    @property
    def REQUEST_MORE_INFORMATION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REQUEST_MORE_INFORMATION"))
    

    

class ClaimTypeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ClaimType")
        self._values: typing.Set[str] = set([ "AUTO_ACCIDENT",  "PROPERTY_DAMAGE",  "LIABILITY",  "PERSONAL_INJURY",  "THEFT",  "FIRE_DAMAGE",  "WATER_DAMAGE",  "VANDALISM",  "OTHER", ])
        self._vals = ClaimTypeValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ClaimTypeValues":
        return self._vals


class ClaimTypeViewer(ClaimTypeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ClaimTypeValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def AUTO_ACCIDENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("AUTO_ACCIDENT"))
    

    @property
    def PROPERTY_DAMAGE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PROPERTY_DAMAGE"))
    

    @property
    def LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LIABILITY"))
    

    @property
    def PERSONAL_INJURY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PERSONAL_INJURY"))
    

    @property
    def THEFT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("THEFT"))
    

    @property
    def FIRE_DAMAGE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("FIRE_DAMAGE"))
    

    @property
    def WATER_DAMAGE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("WATER_DAMAGE"))
    

    @property
    def VANDALISM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("VANDALISM"))
    

    @property
    def OTHER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("OTHER"))
    

    

class ConfidenceLevelAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ConfidenceLevel")
        self._values: typing.Set[str] = set([ "VERY_LOW",  "LOW",  "MEDIUM",  "HIGH",  "VERY_HIGH", ])
        self._vals = ConfidenceLevelValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ConfidenceLevelValues":
        return self._vals


class ConfidenceLevelViewer(ConfidenceLevelAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ConfidenceLevelValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def VERY_LOW(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("VERY_LOW"))
    

    @property
    def LOW(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LOW"))
    

    @property
    def MEDIUM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDIUM"))
    

    @property
    def HIGH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("HIGH"))
    

    @property
    def VERY_HIGH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("VERY_HIGH"))
    

    

class CoverageStatusAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("CoverageStatus")
        self._values: typing.Set[str] = set([ "COVERED",  "NOT_COVERED",  "PARTIALLY_COVERED",  "REQUIRES_INVESTIGATION",  "POLICY_NOT_FOUND", ])
        self._vals = CoverageStatusValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "CoverageStatusValues":
        return self._vals


class CoverageStatusViewer(CoverageStatusAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class CoverageStatusValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def COVERED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("COVERED"))
    

    @property
    def NOT_COVERED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NOT_COVERED"))
    

    @property
    def PARTIALLY_COVERED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PARTIALLY_COVERED"))
    

    @property
    def REQUIRES_INVESTIGATION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REQUIRES_INVESTIGATION"))
    

    @property
    def POLICY_NOT_FOUND(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("POLICY_NOT_FOUND"))
    

    

class DocumentTypeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("DocumentType")
        self._values: typing.Set[str] = set([ "POLICE_REPORT",  "MEDICAL_REPORT",  "REPAIR_ESTIMATE",  "PHOTO_EVIDENCE",  "INSURANCE_FORM",  "RECEIPT",  "INVOICE",  "WITNESS_STATEMENT",  "CORRESPONDENCE",  "OTHER_DOCUMENT", ])
        self._vals = DocumentTypeValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "DocumentTypeValues":
        return self._vals


class DocumentTypeViewer(DocumentTypeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class DocumentTypeValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def POLICE_REPORT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("POLICE_REPORT"))
    

    @property
    def MEDICAL_REPORT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDICAL_REPORT"))
    

    @property
    def REPAIR_ESTIMATE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REPAIR_ESTIMATE"))
    

    @property
    def PHOTO_EVIDENCE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PHOTO_EVIDENCE"))
    

    @property
    def INSURANCE_FORM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("INSURANCE_FORM"))
    

    @property
    def RECEIPT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("RECEIPT"))
    

    @property
    def INVOICE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("INVOICE"))
    

    @property
    def WITNESS_STATEMENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("WITNESS_STATEMENT"))
    

    @property
    def CORRESPONDENCE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CORRESPONDENCE"))
    

    @property
    def OTHER_DOCUMENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("OTHER_DOCUMENT"))
    

    

class EmailTypeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("EmailType")
        self._values: typing.Set[str] = set([ "CLAIM_SUBMISSION",  "CLAIM_INQUIRY",  "POLICY_QUESTION",  "BILLING_INQUIRY",  "GENERAL_INQUIRY",  "SPAM",  "NOT_INSURANCE_RELATED", ])
        self._vals = EmailTypeValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "EmailTypeValues":
        return self._vals


class EmailTypeViewer(EmailTypeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class EmailTypeValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def CLAIM_SUBMISSION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CLAIM_SUBMISSION"))
    

    @property
    def CLAIM_INQUIRY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CLAIM_INQUIRY"))
    

    @property
    def POLICY_QUESTION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("POLICY_QUESTION"))
    

    @property
    def BILLING_INQUIRY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("BILLING_INQUIRY"))
    

    @property
    def GENERAL_INQUIRY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("GENERAL_INQUIRY"))
    

    @property
    def SPAM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("SPAM"))
    

    @property
    def NOT_INSURANCE_RELATED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NOT_INSURANCE_RELATED"))
    

    

class LegalRiskAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("LegalRisk")
        self._values: typing.Set[str] = set([ "VERY_LOW",  "LOW",  "MEDIUM",  "HIGH",  "VERY_HIGH", ])
        self._vals = LegalRiskValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "LegalRiskValues":
        return self._vals


class LegalRiskViewer(LegalRiskAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class LegalRiskValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def VERY_LOW(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("VERY_LOW"))
    

    @property
    def LOW(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LOW"))
    

    @property
    def MEDIUM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDIUM"))
    

    @property
    def HIGH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("HIGH"))
    

    @property
    def VERY_HIGH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("VERY_HIGH"))
    

    

class LiabilityAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("LiabilityAssessment")
        self._values: typing.Set[str] = set([ "CLEAR_LIABILITY",  "DISPUTED_LIABILITY",  "NO_LIABILITY",  "REQUIRES_INVESTIGATION", ])
        self._vals = LiabilityAssessmentValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "LiabilityAssessmentValues":
        return self._vals


class LiabilityAssessmentViewer(LiabilityAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class LiabilityAssessmentValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def CLEAR_LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CLEAR_LIABILITY"))
    

    @property
    def DISPUTED_LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("DISPUTED_LIABILITY"))
    

    @property
    def NO_LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NO_LIABILITY"))
    

    @property
    def REQUIRES_INVESTIGATION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REQUIRES_INVESTIGATION"))
    

    

class NextActionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("NextAction")
        self._values: typing.Set[str] = set([ "CREATE_ZENDESK_TICKET",  "SCHEDULE_INSPECTION",  "REQUEST_DOCUMENTS",  "CONTACT_CUSTOMER",  "ESCALATE_TO_MANAGER",  "CLOSE_INQUIRY",  "TRANSFER_TO_SPECIALIST", ])
        self._vals = NextActionValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "NextActionValues":
        return self._vals


class NextActionViewer(NextActionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class NextActionValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def CREATE_ZENDESK_TICKET(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CREATE_ZENDESK_TICKET"))
    

    @property
    def SCHEDULE_INSPECTION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("SCHEDULE_INSPECTION"))
    

    @property
    def REQUEST_DOCUMENTS(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REQUEST_DOCUMENTS"))
    

    @property
    def CONTACT_CUSTOMER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CONTACT_CUSTOMER"))
    

    @property
    def ESCALATE_TO_MANAGER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("ESCALATE_TO_MANAGER"))
    

    @property
    def CLOSE_INQUIRY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CLOSE_INQUIRY"))
    

    @property
    def TRANSFER_TO_SPECIALIST(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("TRANSFER_TO_SPECIALIST"))
    

    

class UrgencyLevelAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("UrgencyLevel")
        self._values: typing.Set[str] = set([ "LOW",  "MEDIUM",  "HIGH",  "CRITICAL", ])
        self._vals = UrgencyLevelValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "UrgencyLevelValues":
        return self._vals


class UrgencyLevelViewer(UrgencyLevelAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class UrgencyLevelValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def LOW(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LOW"))
    

    @property
    def MEDIUM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDIUM"))
    

    @property
    def HIGH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("HIGH"))
    

    @property
    def CRITICAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CRITICAL"))
    

    


__all__ = ["TypeBuilder"]