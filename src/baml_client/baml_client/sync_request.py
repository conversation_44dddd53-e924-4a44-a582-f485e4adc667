###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
from typing import Dict, List, Optional, Union
from typing_extensions import Literal

import baml_py

from . import _baml


class HttpRequest:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeDocument(
        self,
        document_text: str,document_filename: str,claim_context: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AnalyzeDocument",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_context": claim_context,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def AssessLegalRisk(
        self,
        incident_description: str,location: str,parties_involved: str,claim_value: float,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AssessLegalRisk",
        {
          "incident_description": incident_description,"location": location,"parties_involved": parties_involved,"claim_value": claim_value,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ClassifyEmail(
        self,
        email_subject: str,email_body: str,sender_email: str,attachments: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ClassifyEmail",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ValidateEmailClassification(
        self,
        email_subject: str,email_body: str,sender_email: str,attachments: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ValidateEmailClassification",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def VerifyPolicyDetails(
        self,
        policy_number: str,incident_date: str,claim_type: str,policy_data: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "VerifyPolicyDetails",
        {
          "policy_number": policy_number,"incident_date": incident_date,"claim_type": claim_type,"policy_data": policy_data,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    


class HttpStreamRequest:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeDocument(
        self,
        document_text: str,document_filename: str,claim_context: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AnalyzeDocument",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_context": claim_context,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def AssessLegalRisk(
        self,
        incident_description: str,location: str,parties_involved: str,claim_value: float,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AssessLegalRisk",
        {
          "incident_description": incident_description,"location": location,"parties_involved": parties_involved,"claim_value": claim_value,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ClassifyEmail(
        self,
        email_subject: str,email_body: str,sender_email: str,attachments: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ClassifyEmail",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ValidateEmailClassification(
        self,
        email_subject: str,email_body: str,sender_email: str,attachments: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ValidateEmailClassification",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def VerifyPolicyDetails(
        self,
        policy_number: str,incident_date: str,claim_type: str,policy_data: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "VerifyPolicyDetails",
        {
          "policy_number": policy_number,"incident_date": incident_date,"claim_type": claim_type,"policy_data": policy_data,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    


__all__ = ["HttpRequest", "HttpStreamRequest"]