###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
from typing import Dict, List, Optional, Union, cast
from typing_extensions import Literal

import baml_py

from . import _baml
from .types import Checked, Check


class LlmResponseParser:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeDocument(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.DocumentAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AnalyzeDocument",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.DocumentAnalysis, parsed)
    
    def AssessLegalRisk(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.LegalAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AssessLegalRisk",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.LegalAnalysis, parsed)
    
    def ClassifyEmail(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.EmailAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ClassifyEmail",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.EmailAnalysis, parsed)
    
    def ValidateEmailClassification(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.EmailAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ValidateEmailClassification",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.EmailAnalysis, parsed)
    
    def VerifyPolicyDetails(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.PolicyVerification:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "VerifyPolicyDetails",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.PolicyVerification, parsed)
    


class LlmStreamParser:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeDocument(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.DocumentAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AnalyzeDocument",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.DocumentAnalysis, parsed)
    
    def AssessLegalRisk(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.LegalAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AssessLegalRisk",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.LegalAnalysis, parsed)
    
    def ClassifyEmail(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.EmailAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ClassifyEmail",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.EmailAnalysis, parsed)
    
    def ValidateEmailClassification(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.EmailAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ValidateEmailClassification",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.EmailAnalysis, parsed)
    
    def VerifyPolicyDetails(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.PolicyVerification:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "VerifyPolicyDetails",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.PolicyVerification, parsed)
    


__all__ = ["LlmResponseParser", "LlmStreamParser"]