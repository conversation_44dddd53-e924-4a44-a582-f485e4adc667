"""
Workflow Coordinator Mo<PERSON>le

Placeholder implementation for workflow coordination functionality.
This will be expanded as part of the workflow engine.
"""

import asyncio
import logging
import datetime
from typing import Dict, Any, Optional, List
from enum import Enum

# Import AI classifier and claims processor
from ..email_processing.multi_model_classifier import MultiModelClassifier
from ..zendesk_integration.claims_processor import ClaimsProcessor

logger = logging.getLogger(__name__)

# Dozzle-compatible logging helper
def dozzle_log(level: str, message: str, **kwargs):
    """Enhanced logging for Dozzle visibility with both structured logs and print statements"""
    import datetime
    timestamp = datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")

    # Print statement for immediate Dozzle visibility
    if kwargs:
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] {level.upper()}: {message} | {extra_info}", flush=True)
    else:
        print(f"[{timestamp}] {level.upper()}: {message}", flush=True)

    # Structured log for detailed analysis (without extra kwargs to avoid Logger._log() error)
    if level.lower() == "info":
        logger.info(message)
    elif level.lower() == "error":
        logger.error(message)
    elif level.lower() == "warning":
        logger.warning(message)
    elif level.lower() == "debug":
        logger.debug(message)
    else:
        logger.info(message, **kwargs)


class WorkflowStatus(Enum):
    """Workflow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"


class WorkflowCoordinator:
    """
    Workflow coordination service for managing insurance claims processing workflows.
    
    This is a placeholder implementation that will be expanded to include:
    - Workflow definition and execution
    - State management
    - Task orchestration
    - Integration with AI models and human approval
    """
    
    def __init__(self, settings: Optional[Any] = None, humanlayer: Optional[Any] = None, performance_tracker: Optional[Any] = None):
        """Initialize the workflow coordinator."""
        self.settings = settings
        self.humanlayer = humanlayer
        self.performance_tracker = performance_tracker
        self.active_workflows: Dict[str, Dict[str, Any]] = {}

        # Initialize AI classifier
        dozzle_log("info", "🤖 [WORKFLOW] Initializing AI classifier...")
        self.ai_classifier = MultiModelClassifier(settings=settings)

        # Initialize claims processor for Zendesk + Supabase integration
        dozzle_log("info", "🏭 [WORKFLOW] Initializing claims processor...")
        self.claims_processor = ClaimsProcessor(settings=settings)
        dozzle_log("info", "✅ [WORKFLOW] AI classifier initialized")

        dozzle_log("info", "🔄 [WORKFLOW] WorkflowCoordinator initialized")
    
    async def start_workflow(self, workflow_id: str, workflow_type: str, data: Dict[str, Any]) -> str:
        """Start a new workflow instance."""
        dozzle_log("info", "🚀 [WORKFLOW] Starting new workflow",
                   workflow_id=workflow_id,
                   workflow_type=workflow_type)
        dozzle_log("info", "📊 [WORKFLOW] Current active workflows count", count=len(self.active_workflows))

        import datetime
        timestamp = datetime.datetime.utcnow().isoformat() + "Z"

        workflow_data = {
            "id": workflow_id,
            "type": workflow_type,
            "status": WorkflowStatus.RUNNING.value,
            "data": data,
            "created_at": timestamp,
            "steps_completed": 0,
            "total_steps": 5  # Placeholder
        }

        self.active_workflows[workflow_id] = workflow_data

        dozzle_log("info", "✅ [WORKFLOW] Workflow created successfully",
                   workflow_id=workflow_id,
                   status=workflow_data["status"],
                   created_at=workflow_data["created_at"])
        dozzle_log("info", "📊 [WORKFLOW] Updated active workflows count", count=len(self.active_workflows))

        # Start AI processing for email workflows
        if workflow_type == "email_processing":
            dozzle_log("info", "🤖 [WORKFLOW] Starting AI classification for email workflow...")
            await self._process_email_with_ai(workflow_id, data)

        return workflow_id
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific workflow."""
        return self.active_workflows.get(workflow_id)
    
    async def list_active_workflows(self) -> List[Dict[str, Any]]:
        """List all active workflows."""
        return list(self.active_workflows.values())
    
    async def process_incoming_email(self, webhook_data: Dict[str, Any]) -> str:
        """Process incoming email from HumanLayer webhook."""
        dozzle_log("info", "📧 [WORKFLOW] Processing incoming email webhook", event_type=webhook_data.get("type"))
        dozzle_log("info", "🔍 [WORKFLOW] Webhook data keys:", keys=list(webhook_data.keys()))

        # 🔍 DEBUG: Enhanced webhook data validation
        dozzle_log("info", "🔍 [WORKFLOW_DEBUG] Complete webhook structure analysis")
        
        # Validate webhook structure according to HumanLayer documentation
        if not webhook_data.get("event"):
            dozzle_log("error", "❌ [WORKFLOW] Missing 'event' field in webhook data",
                       webhook_keys=list(webhook_data.keys()))
            raise ValueError("Invalid webhook data: missing 'event' field")

        # Validate webhook type
        webhook_type = webhook_data.get("type")
        if webhook_type != "agent_email.received":
            dozzle_log("warning", "⚠️ [WORKFLOW] Unexpected webhook type",
                       expected="agent_email.received",
                       actual=webhook_type)

        # Extract email data with comprehensive validation according to HumanLayer format
        event = webhook_data.get("event", {})
        dozzle_log("info", "📬 [WORKFLOW] Extracting email data from event...")
        
        # 🔍 DEBUG: Log raw event data
        dozzle_log("info", "🔍 [WORKFLOW_DEBUG] Raw event data analysis",
                   event_keys=list(event.keys()),
                   event_size=len(str(event)))

        # Extract fields according to official HumanLayer webhook format
        from_address = event.get("from_address", "")
        to_address = event.get("to_address", "")
        subject = event.get("subject", "")
        body_original = event.get("body", "")  # HumanLayer's parsed body (potentially corrupted)
        message_id = event.get("message_id", "")
        raw_email = event.get("raw_email", "")  # Full email with headers
        previous_thread = event.get("previous_thread", [])  # Email thread history
        
        # 🔧 CRITICAL FIX: Parse body from raw_email to bypass HumanLayer corruption
        dozzle_log("info", "🔧 [WORKFLOW_DEBUG] Attempting to parse clean body from raw email to bypass HumanLayer corruption",
                   humanlayer_body_length=len(body_original),
                   humanlayer_body_preview=body_original[:200] + "..." if len(body_original) > 200 else body_original,
                   raw_email_available=bool(raw_email),
                   raw_email_length=len(raw_email))
        
        # Try to parse clean body from raw email
        body_from_raw = self._parse_email_body_from_raw(raw_email)
        
        # Use parsed body if successful, otherwise fall back to HumanLayer body
        if body_from_raw and len(body_from_raw) > len(body_original):
            body = body_from_raw
            body_source = "raw_email_parsed"
            dozzle_log("info", "✅ [WORKFLOW_DEBUG] Using parsed body from raw email (HumanLayer bypass)",
                       source="raw_email_parsed",
                       humanlayer_body_length=len(body_original),
                       parsed_body_length=len(body),
                       improvement_ratio=f"{len(body)/len(body_original):.1f}x")
        else:
            body = body_original
            body_source = "humanlayer_original"
            dozzle_log("warning", "⚠️ [WORKFLOW_DEBUG] Using HumanLayer original body (parsing failed)",
                       source="humanlayer_original",
                       body_length=len(body))
        
        # 🔍 DEBUG: Field-by-field validation according to HumanLayer format
        dozzle_log("info", "🔍 [WORKFLOW_DEBUG] HumanLayer email field validation",
                   from_address_valid=bool(from_address),
                   from_address_length=len(from_address),
                   to_address_valid=bool(to_address),
                   subject_valid=bool(subject),
                   subject_length=len(subject),
                   body_valid=bool(body),
                   body_length=len(body),
                   body_source=body_source,
                   message_id_valid=bool(message_id),
                   raw_email_valid=bool(raw_email),
                   raw_email_length=len(raw_email),
                   previous_thread_count=len(previous_thread) if isinstance(previous_thread, list) else 0)
        
        # 🔍 DEBUG: Check for attachments in raw email (HumanLayer doesn't provide structured attachments)
        has_attachments = False
        attachments_mentioned = False
        if raw_email:
            # Check for attachment indicators in headers
            attachment_indicators = [
                "Content-Disposition: attachment",
                "Content-Type: application/",
                "filename=",
                "Content-Transfer-Encoding: base64"
            ]
            has_attachments = any(indicator in raw_email for indicator in attachment_indicators)
            
        if body:
            # Check if email body mentions attachments
            attachment_keywords = [
                "attached", "attachment", "please find attached", "enclosed", 
                "document", "file", "report", "pdf", "image"
            ]
            attachments_mentioned = any(keyword in body.lower() for keyword in attachment_keywords)
        
        dozzle_log("info", "🔍 [WORKFLOW_DEBUG] Attachment analysis",
                   has_attachments_in_raw=has_attachments,
                   attachments_mentioned_in_body=attachments_mentioned,
                   raw_email_preview=raw_email[:500] + "..." if len(raw_email) > 500 else raw_email)
        
        # 🔍 DEBUG: Content validation (check for corruption)
        corruption_score = 0
        if body:
            # Check for common corruption patterns
            corruption_indicators = [
                len(body) < 50 and not any(word in body.lower() for word in ['hi', 'hello', 'thanks', 'urgent']),  # Too short but not a quick message
                body.count(' ') < 3 and len(body) > 20,  # Too few spaces for length
                any(char in body for char in ['#', '2B0', 'ofloxacin']) and 'medical' not in body.lower(),  # Corruption patterns from logs
                not any(word in body.lower() for word in ['claim', 'incident', 'insurance', 'injury', 'damage', 'hello', 'hi', 'thanks', 'urgent', 'please'])  # No recognizable content
            ]
            
            corruption_score = sum(corruption_indicators)
            is_likely_corrupted = corruption_score >= 2
            
            dozzle_log("info", "🔍 [WORKFLOW_DEBUG] Email content corruption analysis",
                       body_preview=body[:300] + "..." if len(body) > 300 else body,
                       corruption_score=corruption_score,
                       is_likely_corrupted=is_likely_corrupted,
                       indicators=corruption_indicators)
            
            if is_likely_corrupted:
                dozzle_log("error", "⚠️ [WORKFLOW] POTENTIAL EMAIL CORRUPTION DETECTED",
                           body_content=body,
                           corruption_score=corruption_score,
                           recommended_action="Manual review required - check HumanLayer webhook format")
        else:
            is_likely_corrupted = False

        # Enhanced email data structure following HumanLayer format
        email_data = {
            # Core HumanLayer fields
            "from_address": from_address,
            "to_address": to_address,
            "subject": subject,
            "body": body,  # Now using cleaned body (either parsed or original)
            "body_original": body_original,  # Keep original for comparison
            "body_source": body_source,  # Track which source was used
            "message_id": message_id,
            "raw_email": raw_email,
            "previous_thread": previous_thread,
            
            # Compatibility fields for our system
            "sender_email": from_address,
            "sender_name": self._extract_sender_name(from_address),
            "received_at": webhook_data.get("received_at") or datetime.datetime.utcnow().isoformat() + "Z",
            
            # Attachment analysis (since HumanLayer doesn't provide structured attachments)
            "attachments": [],  # Empty array since HumanLayer doesn't provide structured attachments
            "has_attachments": has_attachments,
            "attachments_mentioned": attachments_mentioned,
            "attachment_count": 0,  # Will need to parse from raw_email if needed
            
            # Processing metadata
            "processed_at": datetime.datetime.utcnow().isoformat() + "Z",
            "webhook_metadata": {
                "event_type": webhook_data.get("type"),
                "is_test": webhook_data.get("is_test", False),
                "webhook_id": webhook_data.get("id"),
                "corruption_detected": is_likely_corrupted,
                "humanlayer_format": True,
                "body_parsing": {
                    "source": body_source,
                    "original_length": len(body_original),
                    "final_length": len(body),
                    "improvement_ratio": len(body) / len(body_original) if len(body_original) > 0 else 1.0
                }
            }
        }

        # 🔍 DEBUG: Final email data structure
        dozzle_log("info", "🔍 [WORKFLOW_DEBUG] Final HumanLayer email data structure",
                   email_data_keys=list(email_data.keys()),
                   email_data_size=len(str(email_data)))

        dozzle_log("info", "📧 [WORKFLOW] HumanLayer email data extracted and validated:",
                    from_address=email_data["from_address"],
                    to_address=email_data["to_address"],
                    subject=email_data["subject"],
                    message_id=email_data["message_id"],
                    body_length=len(email_data.get("body", "")),
                    body_source=email_data["body_source"],
                    body_improvement=f"{email_data['webhook_metadata']['body_parsing']['improvement_ratio']:.1f}x" if email_data["body_source"] == "raw_email_parsed" else "none",
                    has_attachments=email_data["has_attachments"],
                    attachments_mentioned=email_data["attachments_mentioned"],
                    corruption_detected=email_data["webhook_metadata"]["corruption_detected"])

        # Start email processing workflow
        workflow_id = f"email_{email_data.get('message_id', 'unknown')}"
        dozzle_log("info", "🚀 [WORKFLOW] Starting email processing workflow...", workflow_id=workflow_id)

        await self.start_workflow(workflow_id, "email_processing", email_data)

        dozzle_log("info", "✅ [WORKFLOW] Email processing workflow started successfully", workflow_id=workflow_id)
        return workflow_id

    def _extract_sender_name(self, from_address: str) -> str:
        """Extract sender name from email address"""
        if not from_address:
            return ""
        
        # <AUTHOR> <EMAIL>"
        if "<" in from_address and ">" in from_address:
            name_part = from_address.split("<")[0].strip()
            return name_part.strip('"').strip("'")
        
        # Handle format like "<EMAIL>"
        if "@" in from_address:
            local_part = from_address.split("@")[0]
            # Convert underscores/dots to spaces and title case
            return local_part.replace("_", " ").replace(".", " ").title()
        
        return from_address

    async def process_manual_claim(self, claim_data: Dict[str, Any]) -> str:
        """Process manual claim submission."""
        logger.info("📝 [WORKFLOW] Processing manual claim submission")
        logger.info("🔍 [WORKFLOW] Claim data keys:", keys=list(claim_data.keys()))

        claim_id = claim_data.get('claim_id', 'unknown')
        logger.info("📋 [WORKFLOW] Claim details:",
                    claim_id=claim_id,
                    customer_name=claim_data.get('customer_name'),
                    policy_number=claim_data.get('policy_number'),
                    incident_type=claim_data.get('incident_type'))

        # Start manual claim processing workflow
        workflow_id = f"manual_claim_{claim_id}"
        logger.info("🚀 [WORKFLOW] Starting manual claim processing workflow...", workflow_id=workflow_id)

        await self.start_workflow(workflow_id, "manual_claim_processing", claim_data)

        logger.info("✅ [WORKFLOW] Manual claim processing workflow started successfully", workflow_id=workflow_id)
        return workflow_id

    async def get_claim_status(self, claim_id: str) -> Optional[Dict[str, Any]]:
        """Get claim processing status by claim ID."""
        logger.info("🔍 [WORKFLOW] Getting claim status", claim_id=claim_id)
        logger.info("📊 [WORKFLOW] Searching through active workflows:", count=len(self.active_workflows))

        # Log all active workflow IDs for debugging
        workflow_ids = list(self.active_workflows.keys())
        logger.info("🔍 [WORKFLOW] Active workflow IDs:", workflow_ids=workflow_ids)

        # Look for workflows that match this claim ID
        for workflow_id, workflow in self.active_workflows.items():
            logger.info("🔍 [WORKFLOW] Checking workflow:",
                        workflow_id=workflow_id,
                        workflow_type=workflow.get("type"),
                        data_claim_id=workflow.get("data", {}).get("claim_id"))

            if claim_id in workflow_id or workflow.get("data", {}).get("claim_id") == claim_id:
                logger.info("✅ [WORKFLOW] Found matching workflow",
                            workflow_id=workflow_id,
                            claim_id=claim_id)

                status_response = {
                    "claim_id": claim_id,
                    "workflow_id": workflow_id,
                    "status": workflow.get("status"),
                    "type": workflow.get("type"),
                    "created_at": workflow.get("created_at"),
                    "steps_completed": workflow.get("steps_completed", 0),
                    "total_steps": workflow.get("total_steps", 5),
                    "data": workflow.get("data", {})
                }

                logger.info("📤 [WORKFLOW] Returning status response:", response=status_response)
                return status_response

        logger.warning("❌ [WORKFLOW] Claim not found",
                       claim_id=claim_id,
                       searched_workflows=len(self.active_workflows))
        return None

    async def _process_email_with_ai(self, workflow_id: str, email_data: Dict[str, Any]) -> None:
        """Process email with AI classification."""
        try:
            dozzle_log("info", "🤖 [AI_WORKFLOW] Starting AI classification for email",
                       workflow_id=workflow_id,
                       subject=email_data.get("subject", "")[:50],
                       from_address=email_data.get("from_address", ""))

            # Update workflow status
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["steps_completed"] = 1
                self.active_workflows[workflow_id]["current_step"] = "ai_classification"

            # Extract email details for AI processing (HumanLayer format)
            subject = email_data.get("subject", "")
            body = email_data.get("body", "")
            sender = email_data.get("from_address", "")
            raw_email = email_data.get("raw_email", "")
            has_attachments = email_data.get("has_attachments", False)
            attachments_mentioned = email_data.get("attachments_mentioned", False)

            # 🔍 DETAILED EMAIL CONTENT LOGGING BEFORE AI CLASSIFICATION
            dozzle_log("info", "📧 [EMAIL_CONTENT] Raw HumanLayer email data received before AI classification",
                       workflow_id=workflow_id)

            dozzle_log("info", "📋 [EMAIL_SUBJECT] Full subject line",
                       subject=subject,
                       subject_length=len(subject))

            dozzle_log("info", "👤 [EMAIL_SENDER] Sender information",
                       sender=sender,
                       sender_type=type(sender).__name__)

            dozzle_log("info", "📝 [EMAIL_BODY] Full email body content | body={} | body_length={} | body_preview={}".format(
                body, 
                len(body),
                body[:500] + "..." if len(body) > 500 else body
            ))

            # Enhanced body source logging
            body_source = email_data.get("body_source", "unknown")
            if body_source == "raw_email_parsed":
                original_length = email_data.get("body_original", "")
                improvement = len(body) / len(original_length) if original_length else 1.0
                dozzle_log("info", "🔧 [EMAIL_BODY_SOURCE] Using parsed body from raw email (HumanLayer bypass success)",
                           source="raw_email_parsed",
                           original_body_length=len(original_length),
                           parsed_body_length=len(body),
                           improvement_ratio=f"{improvement:.1f}x",
                           original_body_preview=original_length[:200] + "..." if len(original_length) > 200 else original_length)
            else:
                dozzle_log("info", "⚠️ [EMAIL_BODY_SOURCE] Using HumanLayer original body",
                           source="humanlayer_original",
                           body_length=len(body))

            # Enhanced attachment logging for HumanLayer format
            dozzle_log("info", "📎 [EMAIL_ATTACHMENTS] HumanLayer attachment analysis",
                       has_attachments=has_attachments,
                       attachments_mentioned=attachments_mentioned,
                       raw_email_length=len(raw_email))

            if raw_email:
                # Extract attachment information from raw email if available
                attachment_lines = [line for line in raw_email.split('\n') if 
                                  any(indicator in line for indicator in 
                                      ["Content-Disposition:", "filename=", "Content-Type: application/"])]
                
                dozzle_log("info", "📎 [RAW_EMAIL_ATTACHMENTS] Attachment indicators found in raw email",
                           attachment_lines_count=len(attachment_lines),
                           attachment_lines=attachment_lines[:5])  # Show first 5 lines

            # Log corruption detection results
            corruption_detected = email_data.get("webhook_metadata", {}).get("corruption_detected", False)
            if corruption_detected:
                dozzle_log("warning", "⚠️ [EMAIL_CORRUPTION] Email corruption detected before AI processing",
                           subject=subject,
                           body_preview=body[:200])

            dozzle_log("info", "📧 [AI_WORKFLOW] Email details summary for AI processing",
                       subject_length=len(subject),
                       body_length=len(body),
                       sender=sender,
                       has_attachments=has_attachments,
                       attachments_mentioned=attachments_mentioned,
                       corruption_detected=corruption_detected)

            # Create attachment list for AI (even if empty, mention attachment status)
            attachment_filenames = []
            if attachments_mentioned:
                # If attachments are mentioned but not provided, indicate this to AI
                attachment_filenames.append("mentioned_but_not_provided")
            
            # Run AI classification with enhanced attachment information
            dozzle_log("info", "🚀 [AI_WORKFLOW] Calling multi-model AI classifier...")
            classification_result = await self.ai_classifier.classify_email(
                email_subject=subject,
                email_body=body,
                sender_email=sender,
                attachments=attachment_filenames
            )

            dozzle_log("info", "✅ [AI_WORKFLOW] AI classification completed",
                       email_type=classification_result.final_analysis.email_type.value,
                       confidence=f"{classification_result.consensus_confidence:.3f}",
                       requires_human_review=classification_result.requires_human_review,
                       processing_time=f"{classification_result.processing_time:.3f}s")

            # Update workflow with AI results
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["ai_classification"] = {
                    "email_type": classification_result.final_analysis.email_type.value,
                    "confidence": classification_result.consensus_confidence,
                    "requires_human_review": classification_result.requires_human_review,
                    "processing_time": classification_result.processing_time,
                    "corruption_detected": corruption_detected,
                    "attachments_analysis": {
                        "has_attachments": has_attachments,
                        "attachments_mentioned": attachments_mentioned,
                        "attachment_filenames": attachment_filenames
                    }
                }
                self.active_workflows[workflow_id]["steps_completed"] = 2
                self.active_workflows[workflow_id]["current_step"] = "classification_complete"

                # Check if this is a claim
                email_type_lower = classification_result.final_analysis.email_type.value.lower()
                claim_types = ["claim", "insurance_claim", "claim_submission", "claim_inquiry", "claim_update"]

                if email_type_lower in claim_types or "claim" in email_type_lower:
                    dozzle_log("info", "🎯 [AI_WORKFLOW] Email classified as CLAIM",
                               workflow_id=workflow_id,
                               email_type=classification_result.final_analysis.email_type.value)
                    self.active_workflows[workflow_id]["is_claim"] = True

                    # Process claim: Create Supabase record + Zendesk ticket
                    await self._process_claim_workflow(workflow_id, email_data, classification_result)

                else:
                    dozzle_log("info", "📝 [AI_WORKFLOW] Email classified as NON-CLAIM",
                               workflow_id=workflow_id,
                               email_type=classification_result.final_analysis.email_type.value)
                    self.active_workflows[workflow_id]["is_claim"] = False

            dozzle_log("info", "🎉 [AI_WORKFLOW] Email AI processing completed successfully",
                       workflow_id=workflow_id)

        except Exception as e:
            dozzle_log("error", "❌ [AI_WORKFLOW] AI classification failed",
                       workflow_id=workflow_id,
                       error=str(e),
                       error_type=type(e).__name__)

            # Update workflow with error status
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["status"] = WorkflowStatus.FAILED.value
                self.active_workflows[workflow_id]["error"] = str(e)

    async def _process_claim_workflow(self,
                                 workflow_id: str,
                                 email_data: Dict[str, Any],
                                 classification_result: Any) -> None:
        """
        Process claim workflow: Create Supabase record + Zendesk ticket

        Args:
            workflow_id: Workflow ID from email processing
            email_data: Original email information
            classification_result: AI analysis results
        """
        try:
            dozzle_log("info", "🏭 [AI_WORKFLOW] Starting claim processing workflow",
                      workflow_id=workflow_id)

            # Convert classification result to dictionary format
            classification_dict = {
                'final_analysis': {
                    'email_type': classification_result.final_analysis.email_type.value,
                    'claim_type': classification_result.final_analysis.claim_type.value if classification_result.final_analysis.claim_type else 'general',
                    'urgency_level': classification_result.final_analysis.urgency_level.value,
                    'confidence_level': classification_result.final_analysis.confidence.value,
                    'extracted_details': {
                        'policy_number': getattr(classification_result.final_analysis, 'policy_number', None),
                        'incident_date': getattr(classification_result.final_analysis, 'incident_date', None),
                        'incident_location': getattr(classification_result.final_analysis, 'incident_location', None),
                        'claimant_name': getattr(classification_result.final_analysis, 'claimant_name', None),
                        'estimated_value': getattr(classification_result.final_analysis, 'estimated_value', None)
                    }
                },
                'consensus_confidence': classification_result.consensus_confidence,
                'consensus_data': {
                    'primary_confidence': getattr(classification_result, 'primary_confidence', 0.0),
                    'validation_confidence': getattr(classification_result, 'validation_confidence', 0.0),
                    'agreement_score': getattr(classification_result, 'agreement_score', 0.0)
                }
            }

            # Extract attachments if present (placeholder for now)
            attachments = email_data.get('attachments', [])

            # Process claim through claims processor
            processing_result = await self.claims_processor.process_claim_email(
                workflow_id=workflow_id,
                email_data=email_data,
                classification_result=classification_dict,
                attachments=attachments
            )

            # Update workflow with processing results
            self.active_workflows[workflow_id].update({
                "claim_processing": processing_result,
                "claim_id": processing_result.get('claim_id'),
                "zendesk_ticket_id": processing_result.get('zendesk_ticket_id'),
                "zendesk_ticket_url": processing_result.get('zendesk_ticket_url'),
                "steps_completed": 3,
                "current_step": "claim_processed"
            })

            dozzle_log("info", "✅ [AI_WORKFLOW] Claim processing completed successfully",
                      workflow_id=workflow_id,
                      claim_id=processing_result.get('claim_id'),
                      zendesk_ticket_id=processing_result.get('zendesk_ticket_id'))

        except Exception as e:
            dozzle_log("error", "❌ [AI_WORKFLOW] Claim processing failed",
                      workflow_id=workflow_id,
                      error=str(e))

            # Update workflow with error
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id].update({
                    "claim_processing_error": str(e),
                    "current_step": "claim_processing_failed"
                })

            # Don't re-raise to avoid breaking the main workflow
            # The error is logged and stored in workflow state

    def get_status(self) -> Dict[str, Any]:
        """Get coordinator status."""
        return {
            "status": "operational",
            "service": "workflow_coordinator",
            "active_workflows": len(self.active_workflows),
            "version": "1.0.0"
        }

    def _parse_email_body_from_raw(self, raw_email: str) -> str:
        """
        Parse the actual email body from raw_email since HumanLayer's body field can be corrupted.
        
        Args:
            raw_email: The raw email content with headers
            
        Returns:
            The clean email body content
        """
        if not raw_email:
            return ""
            
        try:
            # Split into lines
            lines = raw_email.split('\n')
            
            # Find where headers end (empty line)
            body_start_idx = 0
            for i, line in enumerate(lines):
                if line.strip() == '' and i > 0:
                    # Check if previous line looks like a header
                    prev_line = lines[i-1].strip()
                    if ':' in prev_line or prev_line.startswith('--'):
                        body_start_idx = i + 1
                        break
            
            # Extract body content
            body_lines = lines[body_start_idx:]
            
            # Remove MIME boundaries and encoding artifacts
            clean_body_lines = []
            in_attachment = False
            
            for line in body_lines:
                line_clean = line.strip()
                
                # Skip MIME boundaries and attachment sections
                if line_clean.startswith('--') and ('boundary' in line_clean or len(line_clean) > 10):
                    in_attachment = True
                    continue
                elif line_clean.startswith('Content-Type:') or line_clean.startswith('Content-Disposition:'):
                    in_attachment = True
                    continue
                elif line_clean.startswith('Content-Transfer-Encoding:'):
                    in_attachment = True
                    continue
                elif in_attachment and (line_clean == '' or len(line_clean) > 50 and not ' ' in line_clean):
                    # Skip base64 content and empty lines in attachments
                    continue
                elif line_clean and not in_attachment:
                    # This is actual email content
                    in_attachment = False
                    clean_body_lines.append(line.rstrip())
                elif line_clean == '' and not in_attachment:
                    clean_body_lines.append('')
            
            # Join and clean up
            body = '\n'.join(clean_body_lines).strip()
            
            # Remove common email artifacts
            body = body.replace('\r', '')
            
            # If body is still very short or looks corrupted, return original
            if len(body) < 50 and not any(word in body.lower() for word in ['hi', 'hello', 'dear', 'thank']):
                dozzle_log("warning", "⚠️ [EMAIL_PARSING] Raw email parsing failed, using HumanLayer body",
                           raw_body_length=len(body),
                           raw_body_preview=body[:100])
                return ""
            
            dozzle_log("info", "✅ [EMAIL_PARSING] Successfully parsed body from raw email",
                       original_length=len(raw_email),
                       parsed_body_length=len(body),
                       body_preview=body[:200] + "..." if len(body) > 200 else body)
            
            return body
            
        except Exception as e:
            dozzle_log("error", "❌ [EMAIL_PARSING] Failed to parse body from raw email",
                       error=str(e),
                       error_type=type(e).__name__)
            return ""
