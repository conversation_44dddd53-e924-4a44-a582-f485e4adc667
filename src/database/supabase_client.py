"""
🗄️ Supabase Client for Zurich Claims Processing

Provides database operations and file storage for:
- Claims data management
- Attachment storage with organized folder structure
- Secure file access with signed URLs
- Comprehensive error handling and retry logic
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
import uuid
import json

from supabase import create_client, Client
from supabase.lib.client_options import ClientOptions
import structlog

from ..config.settings import Settings
from ..utils.dozzle_logger import dozzle_log

logger = structlog.get_logger(__name__)


class SupabaseClient:
    """
    Comprehensive Supabase client for claims processing
    
    Features:
    - Database operations with retry logic
    - File storage with organized folder structure
    - Signed URL generation for secure file access
    - Comprehensive error handling and logging
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client: Optional[Client] = None
        self.storage_bucket = "claims-attachments"
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Supabase client with proper configuration"""
        try:
            # Use service role key for full database access (bypasses RLS)
            self.client = create_client(
                supabase_url=self.settings.supabase_url,
                supabase_key=self.settings.supabase_service_role_key,
                options=ClientOptions(
                    auto_refresh_token=False,  # Service role doesn't need token refresh
                    persist_session=False     # Service role doesn't need session persistence
                )
            )
            
            dozzle_log("info", "✅ [SUPABASE] Client initialized successfully",
                      url=self.settings.supabase_url,
                      bucket=self.storage_bucket)
            
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to initialize client",
                      error=str(e),
                      url=self.settings.supabase_url)
            raise
    
    async def create_claim(self, claim_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new claim record in Supabase

        Args:
            claim_data: Dictionary containing claim information

        Returns:
            Created claim record with id (not claim_id)
        """
        try:
            # Remove claim_id if present (database uses 'id' as primary key)
            if 'claim_id' in claim_data:
                del claim_data['claim_id']

            # Set timestamps (let database handle created_at/updated_at with defaults)
            # Remove manual timestamp setting to use database defaults
            if 'created_at' in claim_data:
                del claim_data['created_at']
            if 'updated_at' in claim_data:
                del claim_data['updated_at']

            dozzle_log("info", "📝 [SUPABASE] Creating claim record",
                      workflow_id=claim_data.get('workflow_id'))

            # Insert claim record
            result = self.client.table('claims').insert(claim_data).execute()

            if result.data:
                created_claim = result.data[0]
                dozzle_log("info", "✅ [SUPABASE] Claim created successfully",
                          claim_id=created_claim['id'],  # Database uses 'id' field
                          workflow_id=created_claim['workflow_id'])
                return created_claim
            else:
                raise Exception("No data returned from insert operation")

        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to create claim",
                      workflow_id=claim_data.get('workflow_id'),
                      error=str(e))
            raise
    
    async def update_claim(self, claim_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing claim record
        
        Args:
            claim_id: UUID of the claim to update
            updates: Dictionary of fields to update
            
        Returns:
            Updated claim record
        """
        try:
            # Add updated timestamp
            updates['updated_at'] = datetime.utcnow().isoformat()
            
            dozzle_log("info", "📝 [SUPABASE] Updating claim record",
                      claim_id=claim_id,
                      fields=list(updates.keys()))
            
            result = self.client.table('claims').update(updates).eq('id', claim_id).execute()
            
            if result.data:
                updated_claim = result.data[0]
                dozzle_log("info", "✅ [SUPABASE] Claim updated successfully",
                          claim_id=claim_id)
                return updated_claim
            else:
                raise Exception(f"Claim not found: {claim_id}")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to update claim",
                      claim_id=claim_id,
                      error=str(e))
            raise
    
    async def get_claim_by_workflow_id(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve claim by workflow_id
        
        Args:
            workflow_id: Workflow ID from email processing
            
        Returns:
            Claim record or None if not found
        """
        try:
            result = self.client.table('claims').select('*').eq('workflow_id', workflow_id).execute()
            
            if result.data:
                claim = result.data[0]
                dozzle_log("info", "✅ [SUPABASE] Claim retrieved successfully",
                          workflow_id=workflow_id,
                          claim_id=claim['id'])
                return claim
            else:
                dozzle_log("info", "ℹ️ [SUPABASE] Claim not found",
                          workflow_id=workflow_id)
                return None
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to retrieve claim",
                      workflow_id=workflow_id,
                      error=str(e))
            raise
    
    def _generate_storage_path(self, workflow_id: str, filename: str) -> str:
        """
        Generate organized storage path for attachments
        Format: claims/2024/12/28/{workflow_id}/filename.ext
        
        Args:
            workflow_id: Workflow ID for organization
            filename: Original filename
            
        Returns:
            Organized storage path
        """
        now = datetime.utcnow()
        date_path = f"{now.year:04d}/{now.month:02d}/{now.day:02d}"
        
        # Clean workflow_id for filesystem safety
        safe_workflow_id = workflow_id.replace('<', '').replace('>', '').replace('@', '_at_')
        
        return f"claims/{date_path}/{safe_workflow_id}/{filename}"
    
    async def upload_attachment(self, 
                              claim_id: str,
                              workflow_id: str,
                              file_content: bytes,
                              filename: str,
                              content_type: str) -> Dict[str, Any]:
        """
        Upload attachment to Supabase Storage and create metadata record
        
        Args:
            claim_id: UUID of the associated claim
            workflow_id: Workflow ID for organization
            file_content: Binary file content
            filename: Original filename
            content_type: MIME type of the file
            
        Returns:
            Attachment record with storage information
        """
        try:
            # Generate storage path
            storage_path = self._generate_storage_path(workflow_id, filename)
            
            dozzle_log("info", "📎 [SUPABASE] Uploading attachment",
                      claim_id=claim_id,
                      filename=filename,
                      storage_path=storage_path,
                      size=len(file_content))
            
            # Upload file to Supabase Storage using correct API
            try:
                # Use the proper Supabase storage upload method
                upload_result = self.client.storage.from_(self.storage_bucket).upload(
                    file=file_content,
                    path=storage_path,
                    file_options={
                        "content-type": content_type,
                        "cache-control": "3600",
                        "upsert": "false"  # Don't overwrite existing files
                    }
                )
                
                dozzle_log("info", "✅ [SUPABASE] File uploaded to storage successfully",
                          storage_path=storage_path,
                          bucket=self.storage_bucket)
                
            except Exception as storage_error:
                dozzle_log("error", "❌ [SUPABASE] Storage upload failed",
                          storage_path=storage_path,
                          error=str(storage_error))
                raise Exception(f"Storage upload failed: {storage_error}")
            
            # Create attachment metadata record with proper field names
            attachment_data = {
                'workflow_id': workflow_id,
                'claim_id': claim_id,
                'original_filename': filename,
                'storage_path': storage_path,
                'file_size': len(file_content),
                'content_type': content_type,
                'upload_status': 'uploaded',
                'filename': filename,  # User's schema has both original_filename and filename
                'storage_bucket': self.storage_bucket,
                'status': 'uploaded',
                'upload_metadata': {
                    'workflow_id': workflow_id,
                    'upload_timestamp': datetime.utcnow().isoformat(),
                    'content_type': content_type,
                    'file_size': len(file_content)
                }
            }
            
            # Insert attachment record
            result = self.client.table('attachments').insert(attachment_data).execute()
            
            if result.data:
                attachment = result.data[0]
                
                # Generate signed URL for access
                signed_url = await self.get_signed_url(storage_path)
                if signed_url:
                    # Update attachment with signed URL
                    await self.update_attachment(attachment['id'], {
                        'storage_url': signed_url
                    })
                    attachment['storage_url'] = signed_url
                
                dozzle_log("info", "✅ [SUPABASE] Attachment uploaded and recorded successfully",
                          attachment_id=attachment['id'],
                          claim_id=claim_id,
                          filename=filename,
                          storage_url=signed_url[:50] + "..." if signed_url else "None")
                
                return attachment
            else:
                raise Exception("Failed to create attachment metadata")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to upload attachment",
                      claim_id=claim_id,
                      filename=filename,
                      error=str(e))
            raise
    
    async def get_signed_url(self, storage_path: str, expires_in: int = 3600) -> Optional[str]:
        """
        Generate signed URL for secure file access
        
        Args:
            storage_path: Path to file in storage
            expires_in: URL expiration time in seconds (default: 1 hour)
            
        Returns:
            Signed URL or None if failed
        """
        try:
            result = self.client.storage.from_(self.storage_bucket).create_signed_url(
                path=storage_path,
                expires_in=expires_in
            )
            
            if result.get('signedURL'):
                return result['signedURL']
            else:
                dozzle_log("warning", "⚠️ [SUPABASE] Failed to generate signed URL",
                          storage_path=storage_path)
                return None
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Error generating signed URL",
                      storage_path=storage_path,
                      error=str(e))
            return None
    
    async def update_attachment(self, attachment_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update attachment metadata"""
        try:
            result = self.client.table('attachments').update(updates).eq('id', attachment_id).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception(f"Attachment not found: {attachment_id}")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to update attachment",
                      attachment_id=attachment_id,
                      error=str(e))
            raise
    
    async def create_zendesk_ticket_record(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create Zendesk ticket tracking record"""
        try:
            ticket_data.update({
                'id': str(uuid.uuid4()),
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat(),
                'last_sync_at': datetime.utcnow().isoformat()
            })
            
            result = self.client.table('zendesk_tickets').insert(ticket_data).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to create Zendesk ticket record")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to create Zendesk ticket record",
                      error=str(e))
            raise
    
    async def add_claim_history(self, claim_id: str, event_type: str, description: str, 
                               old_values: Optional[Dict] = None, new_values: Optional[Dict] = None,
                               metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """Add entry to claim history for audit trail"""
        try:
            history_data = {
                'id': str(uuid.uuid4()),
                'claim_id': claim_id,
                'event_type': event_type,
                'event_description': description,
                'old_values': old_values,
                'new_values': new_values,
                'triggered_by': 'system',
                'metadata': metadata,  # Maps to metadata_json in model but metadata in DB
                'created_at': datetime.utcnow().isoformat()
            }
            
            result = self.client.table('claim_history').insert(history_data).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to create claim history entry")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to add claim history",
                      claim_id=claim_id,
                      event_type=event_type,
                      error=str(e))
            raise
