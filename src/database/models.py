"""
🗄️ Database Models for Zurich Claims Processing System

Comprehensive database schema for insurance claims management with:
- Claims tracking with workflow_id integration
- Attachment metadata and Supabase Storage links
- Zendesk ticket synchronization
- Complete audit trail and processing history
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum

from pydantic import BaseModel, Field
from sqlalchemy import Column, String, DateTime, JSON, Boolean, Integer, Text, ForeignKey, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class ClaimStatus(str, Enum):
    """Claim processing status"""
    RECEIVED = "received"
    PROCESSING = "processing"
    PENDING_DOCUMENTS = "pending_documents"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    DENIED = "denied"
    CLOSED = "closed"


class ClaimType(str, Enum):
    """Types of insurance claims"""
    AUTO = "auto"
    PROPERTY = "property"
    LIABILITY = "liability"
    WORKERS_COMP = "workers_comp"
    GENERAL = "general"
    OTHER = "other"


class AttachmentStatus(str, Enum):
    """Attachment processing status"""
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"


class Claim(Base):
    """
    Main claims table - stores core claim information and AI analysis results
    Primary key: id (UUID) - matches Supabase schema
    Business key: workflow_id (from email processing)
    """
    __tablename__ = "claims"
    
    # Primary identifiers - using 'id' to match Supabase schema
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_id = Column(String(255), unique=True, nullable=False, index=True)
    
    # Email and source information - make nullable for missing data
    email_subject = Column(Text, nullable=True)
    email_body = Column(Text, nullable=True)
    sender_email = Column(String(255), nullable=True)
    sender_name = Column(String(255), nullable=True)
    received_at = Column(DateTime, nullable=True, default=datetime.utcnow)
    
    # AI Classification Results - make nullable for incomplete processing
    claim_type = Column(String(50), nullable=True)
    urgency_level = Column(String(20), nullable=True)
    confidence_level = Column(String(20), nullable=True)
    ai_classification_result = Column(JSON, nullable=True)  # Full AI analysis result
    consensus_confidence = Column(Numeric(5, 4), nullable=True)  # 0.0000 to 1.0000
    
    # Claim details extracted by AI - all nullable as data may not be available
    policy_number = Column(String(100), nullable=True)
    incident_date = Column(DateTime, nullable=True)
    incident_location = Column(Text, nullable=True)
    estimated_value = Column(Numeric(12, 2), nullable=True)
    claimant_name = Column(String(255), nullable=True)
    claimant_phone = Column(String(50), nullable=True)
    
    # Processing status
    status = Column(String(50), nullable=False, default=ClaimStatus.RECEIVED.value)
    priority_score = Column(Integer, default=0)  # 0-100 priority score
    
    # Zendesk integration
    zendesk_ticket_id = Column(String(50), index=True)
    zendesk_ticket_url = Column(Text)
    zendesk_status = Column(String(50))
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    attachments = relationship("Attachment", back_populates="claim", cascade="all, delete-orphan")
    zendesk_tickets = relationship("ZendeskTicket", back_populates="claim", cascade="all, delete-orphan")
    history = relationship("ClaimHistory", back_populates="claim", cascade="all, delete-orphan")


class Attachment(Base):
    """
    Attachment metadata table - links to files stored in Supabase Storage
    Organized by date-based folder structure: claims/2024/12/28/{workflow_id}/
    Matches user's schema exactly with all nullable fields for missing data
    """
    __tablename__ = "attachments"
    
    # Primary identifiers - matches user's schema
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_id = Column(Text, nullable=False)
    claim_id = Column(UUID(as_uuid=True), ForeignKey("claims.id"), nullable=True)
    
    # File information - matches user's schema with nullable fields
    original_filename = Column(Text, nullable=False)
    storage_path = Column(Text, nullable=False)
    file_size = Column(Integer, nullable=True)  # bigint in schema, nullable
    content_type = Column(Text, nullable=True)
    upload_status = Column(Text, nullable=True, default='uploaded')
    created_at = Column(DateTime, nullable=True, default=datetime.utcnow)
    attachment_id = Column(UUID(as_uuid=True), nullable=True)  # Additional ID field from schema
    filename = Column(Text, nullable=True)
    
    # Supabase Storage information - matches user's schema
    storage_bucket = Column(Text, nullable=True)
    storage_url = Column(Text, nullable=True)
    status = Column(Text, nullable=True)
    
    # OCR and processing information - all nullable
    ocr_text = Column(Text, nullable=True)
    ocr_confidence = Column(Numeric(5, 4), nullable=True)
    document_type = Column(Text, nullable=True)
    
    # Metadata - matches user's schema
    upload_metadata = Column("upload_metadata", JSON, nullable=True)
    processing_metadata = Column(JSON, nullable=True)
    
    # Timestamps - matches user's schema
    uploaded_at = Column(DateTime, nullable=True)
    processed_at = Column(DateTime, nullable=True)
    
    # Relationships
    claim = relationship("Claim", back_populates="attachments")


class ZendeskTicket(Base):
    """
    Zendesk ticket tracking table - maintains sync between claims and Zendesk
    Supports multiple tickets per claim for complex cases
    """
    __tablename__ = "zendesk_tickets"
    
    # Primary identifiers
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    claim_id = Column(UUID(as_uuid=True), ForeignKey("claims.id"), nullable=True)
    
    # Zendesk information
    zendesk_ticket_id = Column(String(50), nullable=False, unique=True, index=True)
    zendesk_url = Column(Text, nullable=False)
    
    # Ticket details
    subject = Column(Text, nullable=False)
    description = Column(Text, nullable=False)
    status = Column(String(50), nullable=False)
    priority = Column(String(20), nullable=False)
    
    # Assignment information
    assignee_id = Column(String(50))
    assignee_email = Column(String(255))
    group_id = Column(String(50))
    
    # AI Enhancement data
    ai_priority_score = Column(Integer)  # AI-calculated priority
    complexity_level = Column(String(20))
    estimated_resolution_hours = Column(Integer)
    
    # Sync status
    sync_status = Column(String(50), nullable=False, default="synced")
    last_sync_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    sync_error = Column(Text)  # Error message if sync fails
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    claim = relationship("Claim", back_populates="zendesk_tickets")


class ClaimHistory(Base):
    """
    Audit trail table - tracks all changes and processing steps for claims
    Provides complete history for compliance and debugging
    Matches user's schema with all nullable fields for missing data
    """
    __tablename__ = "claim_history"
    
    # Primary identifiers
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    claim_id = Column(UUID(as_uuid=True), ForeignKey("claims.id"), nullable=True)
    
    # Event information - matches user's schema
    event_type = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    event_description = Column(Text, nullable=True)  # Additional description field
    
    # Change tracking - nullable for missing data
    old_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)
    metadata_json = Column("metadata", JSON, nullable=True)  # Maps to 'metadata' column in DB
    
    # Context information - all nullable
    triggered_by = Column(Text, nullable=True)
    user_id = Column(Text, nullable=True)
    processing_step = Column(Text, nullable=True)
    context_metadata = Column(JSON, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Timestamps - nullable for missing data
    created_at = Column(DateTime, nullable=True, default=datetime.utcnow)
    
    # Relationships
    claim = relationship("Claim", back_populates="history")


# Pydantic models for API responses and data validation
class ClaimResponse(BaseModel):
    """API response model for claim data"""
    claim_id: str
    workflow_id: str
    email_subject: str
    sender_email: str
    claim_type: str
    status: str
    zendesk_ticket_id: Optional[str] = None
    zendesk_ticket_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class AttachmentResponse(BaseModel):
    """API response model for attachment data"""
    attachment_id: str
    filename: str
    content_type: str
    file_size: int
    storage_url: Optional[str] = None
    status: str
    uploaded_at: datetime
    
    class Config:
        from_attributes = True


class ZendeskTicketResponse(BaseModel):
    """API response model for Zendesk ticket data"""
    zendesk_ticket_id: str
    zendesk_url: str
    subject: str
    status: str
    priority: str
    assignee_email: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True
