"""
🗄️ Database Models and Configuration for Zurich Claims Processing

This module provides database models and configuration for:
- Claims management with Supabase integration
- Attachment storage and metadata
- Zendesk ticket tracking
- Audit trail and processing history
"""

from .models import *
from .supabase_client import SupabaseClient

__all__ = [
    "Claim",
    "Attachment", 
    "ZendeskTicket",
    "ClaimHistory",
    "SupabaseClient"
]
