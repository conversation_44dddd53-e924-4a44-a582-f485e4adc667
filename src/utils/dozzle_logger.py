"""
Dozzle-compatible logging utility for Docker log viewing.
Provides structured logging that's optimized for Dozzle Docker log viewer.
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, Optional


def dozzle_log(level: str, message: str, **kwargs) -> None:
    """
    Log a message in Dozzle-compatible format.
    
    Args:
        level: Log level (info, warning, error, debug)
        message: Main log message
        **kwargs: Additional structured data to include in the log
    """
    # Create timestamp in ISO format
    timestamp = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")
    
    # Normalize log level
    level = level.upper()
    
    # Create structured log entry
    log_entry = {
        "timestamp": timestamp,
        "level": level,
        "message": message,
        **kwargs
    }
    
    # Format for Dozzle (JSON + readable format)
    readable_message = f"[{timestamp}] {level}: {message}"
    
    # Add structured data if present
    if kwargs:
        structured_data = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        readable_message += f" | {structured_data}"
    
    # Print both formats for maximum compatibility
    print(readable_message)  # For Dozzle readability
    
    # Also log to Python logger for structured access
    logger = logging.getLogger("dozzle")
    if level == "DEBUG":
        logger.debug(json.dumps(log_entry))
    elif level == "INFO":
        logger.info(json.dumps(log_entry))
    elif level == "WARNING":
        logger.warning(json.dumps(log_entry))
    elif level == "ERROR":
        logger.error(json.dumps(log_entry))
    else:
        logger.info(json.dumps(log_entry))


def setup_dozzle_logging():
    """Setup logging configuration for Dozzle compatibility."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
