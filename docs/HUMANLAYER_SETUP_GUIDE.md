# 🤝 HumanLayer Configuration Guide for Zurich Claims Processing

## 📋 Overview

Based on your HumanLayer console screenshot and the workflow diagram, here's exactly what you need to configure for the Zurich claims processing system.

## 🔧 HumanLayer Console Configuration

### 1. Agent Triggers Setup (Already Configured ✅)

From your screenshot, you already have:
- **Email Address**: `<EMAIL>`
- **Agent Name**: `Claims Zurich`
- **Created**: `6/28/2025`

### 2. Response Webhook Configuration (REQUIRED)

You need to set up a **Response Webhook** endpoint for `v1beta2.agent_email.received` to route emails to your agent.

**In your HumanLayer console:**
1. Go to **Webhooks** section
2. Add webhook endpoint: `https://your-domain.com/api/humanlayer/webhook`
3. Select event type: `v1beta2.agent_email.received`
4. This will trigger when emails are sent to `<EMAIL>`

## 🚨 **CRITICAL FIX: Email Parsing Issue Resolution**

### **Problem Identified**
The email corruption you were experiencing was caused by our code expecting the **wrong webhook format**. We were parsing HumanLayer webhooks using an outdated format that included structured `attachments` arrays, but HumanLayer's official `v1beta2.agent_email.received` format is different.

### **Root Cause**
- **Expected Format (OLD/WRONG)**: `event.attachments[]` with structured attachment objects
- **Actual HumanLayer Format**: `event.raw_email` with full email headers and body

### **Solution Implemented**

#### **Updated Webhook Parsing (Fixed)**
```python
# ✅ CORRECT: Official HumanLayer v1beta2.agent_email.received format
{
  "type": "agent_email.received",
  "is_test": false,
  "event": {
    "from_address": "<EMAIL>",
    "to_address": "<EMAIL>", 
    "subject": "Slip and Fall Claim",
    "body": "I had a slip and fall incident...",
    "message_id": "<message-id>",
    "raw_email": "From: <EMAIL>\nTo: claims...",
    "previous_thread": []
  }
}
```

#### **Key Changes Made**
1. **Removed**: `event.attachments` parsing (doesn't exist in HumanLayer format)
2. **Added**: `event.raw_email` parsing for full email content
3. **Added**: `event.previous_thread` handling for email threads
4. **Enhanced**: Attachment detection from `raw_email` headers
5. **Improved**: Corruption detection based on HumanLayer format

#### **Attachment Handling Fix**
```python
# ✅ NEW: Parse attachments from raw_email headers
if raw_email:
    attachment_indicators = [
        "Content-Disposition: attachment",
        "Content-Type: application/",
        "filename=",
        "Content-Transfer-Encoding: base64"
    ]
    has_attachments = any(indicator in raw_email for indicator in attachment_indicators)
```

### **Results**
- ✅ **Email content no longer corrupted**
- ✅ **Proper attachment detection**  
- ✅ **Full email threading support**
- ✅ **Corruption detection improved**
- ✅ **Compliant with HumanLayer v1beta2 format**

## 🏗️ Implementation Architecture

### Email Flow Integration
```
Incoming Email → <EMAIL>
                ↓
HumanLayer Webhook → Your FastAPI Endpoint
                ↓
Email Classification (BAML + Multi-Model AI)
                ↓
Workflow Decision: Is Claim? → Yes/No
                ↓
If Yes: Create Zendesk Ticket + Send Acknowledgment
```

## 📧 Email Channel Configuration

### 1. Basic Email Channel Setup
```python
from humanlayer import ContactChannel, EmailContactChannel, HumanLayer

# For human agent approvals
claims_manager_channel = ContactChannel(
    email=EmailContactChannel(
        address="<EMAIL>",  # Your actual claims manager email
        context_about_user="an email with the Zurich claims manager",
        subject="Claims Decision Approval Required",
    )
)

# For compliance team approvals
compliance_channel = ContactChannel(
    email=EmailContactChannel(
        address="<EMAIL>",  # Your compliance team email
        context_about_user="an email with the compliance team",
        subject="Coverage Decision Review Required",
    )
)

hl = HumanLayer(
    api_key="your_humanlayer_api_key",  # Get from console
    contact_channel=claims_manager_channel  # Default channel
)
```

### 2. Email Threading for Customer Communication
```python
def handle_inbound_claim_email(raw_email_content: str, headers: dict) -> str:
    """Handle emails <NAME_EMAIL>"""
    
    message_id = headers["Message-ID"]
    customer_email = headers["From"]
    
    # Create threaded email channel for customer communication
    customer_channel = ContactChannel(
        email=EmailContactChannel(
            address=customer_email,
            context_about_user="an email thread with the customer who submitted the claim",
            in_reply_to_message_id=message_id,  # Reply to original email
            references_message_id=message_id,   # Reference original email
            subject=f"Re: {headers.get('Subject', 'Claim Submission')}"
        )
    )
    
    # Use this channel for customer communications
    customer_hl = HumanLayer(contact_channel=customer_channel)
    
    return customer_hl
```

## 🎯 Workflow-Specific Configurations

### 1. Coverage Decision Approval
```python
@hl.require_approval(
    contact_channel=claims_manager_channel,
    timeout_minutes=120  # 2 hours for decision
)
def deny_coverage(claim_id: str, reason: str, policy_exclusions: list) -> str:
    """Deny coverage - requires human approval"""
    # Implementation here
    pass
```

### 2. High-Value Claim Escalation
```python
@hl.require_approval(
    contact_channel=compliance_channel,
    timeout_minutes=240  # 4 hours for high-value claims
)
def process_high_value_claim(claim_id: str, estimated_value: float) -> str:
    """Process claims over $100,000 - requires compliance approval"""
    if estimated_value > 100000:
        # Requires approval
        pass
    return "approved"
```

### 3. Human-as-Tool for Policy Clarification
```python
# When policy details are unclear
policy_clarification_tool = hl.human_as_tool(
    contact_channel=ContactChannel(
        email=EmailContactChannel(
            address="<EMAIL>",
            context_about_user="an email with the underwriting team",
            subject="Policy Clarification Required"
        )
    )
)
```

## 🔑 Environment Variables Required

Create `.env` file with:
```bash
# HumanLayer Configuration
HUMANLAYER_API_KEY=your_api_key_from_console
HUMANLAYER_WEBHOOK_SECRET=your_webhook_secret

# Email Configuration
CLAIMS_MANAGER_EMAIL=<EMAIL>
COMPLIANCE_EMAIL=<EMAIL>
UNDERWRITING_EMAIL=<EMAIL>

# Webhook Configuration
WEBHOOK_BASE_URL=https://your-domain.com
```

## 📝 Custom Email Templates

### 1. Coverage Decision Template
```python
coverage_decision_template = ContactChannel(
    email=EmailContactChannel(
        address="<EMAIL>",
        context_about_user="an email with the claims manager",
        template="""
        <html>
        <body>
        <h1>Coverage Decision Required</h1>
        
        <div style="background: #f5f5f5; padding: 15px; border-radius: 5px;">
            <h3>Claim Details:</h3>
            <p><strong>Claim ID:</strong> {{ event.spec.kwargs.claim_id }}</p>
            <p><strong>Policy Number:</strong> {{ event.spec.kwargs.policy_number }}</p>
            <p><strong>Estimated Value:</strong> ${{ event.spec.kwargs.estimated_value }}</p>
            <p><strong>AI Recommendation:</strong> {{ event.spec.kwargs.ai_recommendation }}</p>
        </div>
        
        <div style="margin-top: 20px;">
            <a href="{{ urls.base_url }}?approve=true" 
               style="background: #4CAF50; color: white; padding: 10px; 
                      text-decoration: none; border-radius: 5px; margin-right: 10px;">
                Approve Coverage
            </a>
            <a href="{{ urls.base_url }}?reject=true" 
               style="background: #f44336; color: white; padding: 10px; 
                      text-decoration: none; border-radius: 5px;">
                Deny Coverage
            </a>
        </div>
        </body>
        </html>
        """
    )
)
```

## 🚀 Next Steps for Implementation

### 1. Install HumanLayer
```bash
pip install humanlayer
```

### 2. Set up webhook endpoint in your FastAPI app
```python
from fastapi import FastAPI, Request
import hmac
import hashlib

app = FastAPI()

@app.post("/api/humanlayer/webhook")
async def humanlayer_webhook(request: Request):
    """Handle incoming emails from HumanLayer"""
    
    # Verify webhook signature
    signature = request.headers.get("x-humanlayer-signature")
    body = await request.body()
    
    # Verify signature (security)
    expected_signature = hmac.new(
        HUMANLAYER_WEBHOOK_SECRET.encode(),
        body,
        hashlib.sha256
    ).hexdigest()
    
    if signature != f"sha256={expected_signature}":
        raise HTTPException(status_code=401, detail="Invalid signature")
    
    # Process the email
    data = await request.json()
    await process_incoming_claim_email(data)
    
    return {"status": "processed"}
```

### 3. Configure in HumanLayer Console

**What you need to do in https://app.humanlayer.dev/rozie/uc05-claims-liability/getting-started:**

1. **API Keys**: Copy your API key to `.env`
2. **Webhooks**: Add webhook URL: `https://your-domain.com/api/humanlayer/webhook`
3. **Email Triggers**: Already configured ✅
4. **Response Channels**: Configure email addresses for your team
5. **Templates**: Upload custom email templates (optional)

## 🎯 Integration Points with Workflow

1. **Email Received** → HumanLayer webhook triggers your agent
2. **Classification** → BAML + Multi-model AI analysis
3. **Human Approval** → HumanLayer email channels for decisions
4. **Customer Communication** → Threaded email responses
5. **Escalation** → Automatic routing to appropriate team members

This setup ensures seamless human-in-the-loop integration with your Zurich claims workflow! 🚀
