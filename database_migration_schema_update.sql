-- 🔄 Database Schema Migration Script
-- Updates database to match optimized models with nullable fields and consistent IDs
-- Run this in Supabase SQL Editor or your PostgreSQL client

BEGIN;

-- ============================================================================
-- 🗄️ CLAIMS TABLE UPDATES
-- ============================================================================

-- First, drop dependent foreign key constraints
ALTER TABLE IF EXISTS attachments DROP CONSTRAINT IF EXISTS attachments_claim_id_fkey;
ALTER TABLE IF EXISTS zendesk_tickets DROP CONSTRAINT IF EXISTS zendesk_tickets_claim_id_fkey;
ALTER TABLE IF EXISTS claim_history DROP CONSTRAINT IF EXISTS claim_history_claim_id_fkey;

-- Update claims table structure
DO $$
BEGIN
    -- Check if we need to rename the primary key column
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'claims' AND column_name = 'claim_id') THEN
        -- Rename claim_id to id if it exists
        ALTER TABLE claims RENAME COLUMN claim_id TO id;
    END IF;
    
    -- Make fields nullable for missing data handling
    ALTER TABLE claims ALTER COLUMN email_subject DROP NOT NULL;
    ALTER TABLE claims ALTER COLUMN email_body DROP NOT NULL;
    ALTER TABLE claims ALTER COLUMN sender_email DROP NOT NULL;
    ALTER TABLE claims ALTER COLUMN received_at DROP NOT NULL;
    ALTER TABLE claims ALTER COLUMN claim_type DROP NOT NULL;
    ALTER TABLE claims ALTER COLUMN urgency_level DROP NOT NULL;
    ALTER TABLE claims ALTER COLUMN confidence_level DROP NOT NULL;
    
    -- Add missing columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'claims' AND column_name = 'consensus_confidence') THEN
        ALTER TABLE claims ADD COLUMN consensus_confidence NUMERIC(5,4);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'claims' AND column_name = 'ai_classification_result') THEN
        ALTER TABLE claims ADD COLUMN ai_classification_result JSONB;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'claims' AND column_name = 'priority_score') THEN
        ALTER TABLE claims ADD COLUMN priority_score INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'claims' AND column_name = 'zendesk_status') THEN
        ALTER TABLE claims ADD COLUMN zendesk_status TEXT;
    END IF;
    
END $$;

-- ============================================================================
-- 📎 ATTACHMENTS TABLE UPDATES  
-- ============================================================================

-- Update attachments table to match user's schema exactly
DO $$
BEGIN
    -- Add missing columns for user's schema
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attachments' AND column_name = 'workflow_id') THEN
        ALTER TABLE attachments ADD COLUMN workflow_id TEXT NOT NULL DEFAULT 'migration_placeholder';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attachments' AND column_name = 'upload_status') THEN
        ALTER TABLE attachments ADD COLUMN upload_status TEXT DEFAULT 'uploaded';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attachments' AND column_name = 'attachment_id') THEN
        ALTER TABLE attachments ADD COLUMN attachment_id UUID;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attachments' AND column_name = 'filename') THEN
        ALTER TABLE attachments ADD COLUMN filename TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attachments' AND column_name = 'status') THEN
        ALTER TABLE attachments ADD COLUMN status TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attachments' AND column_name = 'document_type') THEN
        ALTER TABLE attachments ADD COLUMN document_type TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attachments' AND column_name = 'processing_metadata') THEN
        ALTER TABLE attachments ADD COLUMN processing_metadata JSONB;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attachments' AND column_name = 'uploaded_at') THEN
        ALTER TABLE attachments ADD COLUMN uploaded_at TIMESTAMPTZ;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attachments' AND column_name = 'processed_at') THEN
        ALTER TABLE attachments ADD COLUMN processed_at TIMESTAMPTZ;
    END IF;
    
    -- Make columns nullable for missing data
    ALTER TABLE attachments ALTER COLUMN claim_id DROP NOT NULL;
    ALTER TABLE attachments ALTER COLUMN file_size DROP NOT NULL;
    ALTER TABLE attachments ALTER COLUMN content_type DROP NOT NULL;
    ALTER TABLE attachments ALTER COLUMN created_at DROP NOT NULL;
    
    -- Update data types to match schema
    ALTER TABLE attachments ALTER COLUMN file_size TYPE BIGINT;
    ALTER TABLE attachments ALTER COLUMN content_type TYPE TEXT;
    ALTER TABLE attachments ALTER COLUMN storage_bucket TYPE TEXT;
    ALTER TABLE attachments ALTER COLUMN storage_url TYPE TEXT;
    
END $$;

-- ============================================================================
-- 📋 CLAIM_HISTORY TABLE UPDATES
-- ============================================================================

-- Update claim_history table structure
DO $$
BEGIN
    -- Make fields nullable for missing data
    ALTER TABLE claim_history ALTER COLUMN claim_id DROP NOT NULL;
    ALTER TABLE claim_history ALTER COLUMN event_description DROP NOT NULL;
    ALTER TABLE claim_history ALTER COLUMN created_at DROP NOT NULL;
    
    -- Add missing columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'claim_history' AND column_name = 'description') THEN
        ALTER TABLE claim_history ADD COLUMN description TEXT;
    END IF;
    
    -- Update data types to TEXT
    ALTER TABLE claim_history ALTER COLUMN event_type TYPE TEXT;
    ALTER TABLE claim_history ALTER COLUMN triggered_by TYPE TEXT;
    ALTER TABLE claim_history ALTER COLUMN user_id TYPE TEXT;
    ALTER TABLE claim_history ALTER COLUMN processing_step TYPE TEXT;
    
END $$;

-- ============================================================================
-- 🎫 ZENDESK_TICKETS TABLE UPDATES
-- ============================================================================

-- Update zendesk_tickets table
DO $$
BEGIN
    -- Make claim_id nullable
    ALTER TABLE zendesk_tickets ALTER COLUMN claim_id DROP NOT NULL;
    
    -- Add missing columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'zendesk_tickets' AND column_name = 'ai_priority_score') THEN
        ALTER TABLE zendesk_tickets ADD COLUMN ai_priority_score INTEGER;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'zendesk_tickets' AND column_name = 'complexity_level') THEN
        ALTER TABLE zendesk_tickets ADD COLUMN complexity_level TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'zendesk_tickets' AND column_name = 'estimated_resolution_hours') THEN
        ALTER TABLE zendesk_tickets ADD COLUMN estimated_resolution_hours INTEGER;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'zendesk_tickets' AND column_name = 'sync_status') THEN
        ALTER TABLE zendesk_tickets ADD COLUMN sync_status TEXT DEFAULT 'synced';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'zendesk_tickets' AND column_name = 'last_sync_at') THEN
        ALTER TABLE zendesk_tickets ADD COLUMN last_sync_at TIMESTAMPTZ DEFAULT NOW();
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'zendesk_tickets' AND column_name = 'sync_error') THEN
        ALTER TABLE zendesk_tickets ADD COLUMN sync_error TEXT;
    END IF;
    
END $$;

-- ============================================================================
-- 🔗 RECREATE FOREIGN KEY CONSTRAINTS
-- ============================================================================

-- Recreate foreign key constraints with correct references
ALTER TABLE attachments 
    ADD CONSTRAINT attachments_claim_id_fkey 
    FOREIGN KEY (claim_id) REFERENCES claims(id);

ALTER TABLE zendesk_tickets 
    ADD CONSTRAINT zendesk_tickets_claim_id_fkey 
    FOREIGN KEY (claim_id) REFERENCES claims(id);

ALTER TABLE claim_history 
    ADD CONSTRAINT claim_history_claim_id_fkey 
    FOREIGN KEY (claim_id) REFERENCES claims(id);

-- ============================================================================
-- 📊 RECREATE INDEXES
-- ============================================================================

-- Recreate indexes for performance
CREATE INDEX IF NOT EXISTS idx_attachments_workflow_id ON attachments USING btree (workflow_id);
CREATE INDEX IF NOT EXISTS idx_attachments_claim_id ON attachments USING btree (claim_id);
CREATE INDEX IF NOT EXISTS idx_claim_history_claim_id ON claim_history USING btree (claim_id);
CREATE INDEX IF NOT EXISTS idx_claim_history_triggered_by ON claim_history USING btree (triggered_by);
CREATE INDEX IF NOT EXISTS idx_claims_workflow_id ON claims USING btree (workflow_id);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_claim_id ON zendesk_tickets USING btree (claim_id);

-- ============================================================================
-- 🧹 CLEANUP TEMPORARY VALUES
-- ============================================================================

-- Update any placeholder workflow_ids that were added during migration
UPDATE attachments 
SET workflow_id = CONCAT('migrated_', id::text) 
WHERE workflow_id = 'migration_placeholder';

-- ============================================================================
-- ✅ MIGRATION COMPLETE
-- ============================================================================

-- Add migration tracking
CREATE TABLE IF NOT EXISTS schema_migrations (
    id SERIAL PRIMARY KEY,
    version TEXT NOT NULL,
    applied_at TIMESTAMPTZ DEFAULT NOW(),
    description TEXT
);

INSERT INTO schema_migrations (version, description) 
VALUES ('2025_01_29_001', 'Updated schema for claim ID consistency and nullable fields');

COMMIT;

-- ============================================================================
-- 📋 VERIFICATION QUERIES
-- ============================================================================

-- Run these to verify the migration was successful:

/*
-- Check claims table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'claims' 
ORDER BY ordinal_position;

-- Check attachments table structure  
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'attachments' 
ORDER BY ordinal_position;

-- Check claim_history table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'claim_history' 
ORDER BY ordinal_position;

-- Check foreign key constraints
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name IN ('claims', 'attachments', 'claim_history', 'zendesk_tickets');
*/ 