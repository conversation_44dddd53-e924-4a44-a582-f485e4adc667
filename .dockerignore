# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Documentation
*.md
docs/
*.rst

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Development
.env.example
.env.local
.env.development
tests/
test_*.py
*_test.py

# Build artifacts
build/
dist/
*.egg-info/

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# Docker
Dockerfile.fast
docker-compose.override.yml
