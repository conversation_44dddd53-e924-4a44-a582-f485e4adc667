#!/usr/bin/env python3
"""
Local test script for Zendesk Direct HTTP API
Tests authentication and mock ticket creation
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append('src')

from src.config.settings import Settings
from src.database.supabase_client import SupabaseClient
from src.zendesk_integration.direct_zendesk_client import DirectZendeskClient

def test_zendesk_authentication():
    """Test Zendesk authentication locally"""
    print("🧪 Testing Zendesk Direct HTTP API Authentication")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check if required environment variables are set
    required_vars = ['ZENDESK_SUBDOMAIN', 'ZENDESK_EMAIL', 'ZENDESK_API_TOKEN']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file:")
        for var in missing_vars:
            print(f"  {var}=your_value_here")
        return False
    
    try:
        # Initialize settings
        settings = Settings()
        print(f"✅ Settings loaded successfully")
        print(f"   URL: {settings.zendesk_url}")
        print(f"   Subdomain: {settings.zendesk_subdomain}")
        print(f"   Email: {settings.zendesk_email}")
        print(f"   Token length: {len(settings.zendesk_token) if settings.zendesk_token else 0}")
        
        # Initialize Supabase client (minimal for testing)
        supabase_client = SupabaseClient(settings)
        print(f"✅ Supabase client initialized")
        
        # Initialize Direct Zendesk client
        print(f"\n🔧 Initializing Direct Zendesk HTTP API client...")
        zendesk_client = DirectZendeskClient(settings, supabase_client)
        print(f"✅ Direct Zendesk client initialized successfully")
        
        return zendesk_client
        
    except Exception as e:
        print(f"❌ Failed to initialize Zendesk client: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        return None

async def test_mock_ticket_creation(zendesk_client):
    """Test creating a mock ticket"""
    print(f"\n🎫 Testing Mock Ticket Creation")
    print("=" * 50)
    
    try:
        # Create mock ticket
        result = await zendesk_client.create_mock_ticket()
        
        print(f"✅ Mock ticket created successfully!")
        print(f"   Ticket ID: {result.get('ticket_id')}")
        print(f"   Ticket URL: {result.get('ticket_url')}")
        print(f"   Status: {result.get('status')}")
        print(f"   Created: {result.get('created_at')}")
        
        return result
        
    except Exception as e:
        print(f"❌ Failed to create mock ticket: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        return None

def test_direct_api_call():
    """Test direct API call without our client"""
    print(f"\n🔍 Testing Direct API Call (without client)")
    print("=" * 50)
    
    import requests
    import base64
    
    try:
        # Load environment variables
        load_dotenv()
        
        subdomain = os.getenv('ZENDESK_SUBDOMAIN')
        email = os.getenv('ZENDESK_EMAIL')
        token = os.getenv('ZENDESK_API_TOKEN')
        
        # Create authentication header
        auth_string = f"{email}/token:{token}"
        auth_bytes = base64.b64encode(auth_string.encode()).decode()
        
        headers = {
            "Authorization": f"Basic {auth_bytes}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        # Test /users/me endpoint
        url = f"https://{subdomain}.zendesk.com/api/v2/users/me.json"
        print(f"📡 Making request to: {url}")
        
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            user_data = response.json()
            user = user_data.get('user', {})
            print(f"✅ Direct API call successful!")
            print(f"   User ID: {user.get('id')}")
            print(f"   Name: {user.get('name')}")
            print(f"   Email: {user.get('email')}")
            print(f"   Role: {user.get('role')}")
            print(f"   Active: {user.get('active')}")
            return True
        else:
            print(f"❌ Direct API call failed")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Direct API call error: {str(e)}")
        return False

async def main():
    """Main test function"""
    print("🚀 Zendesk Direct HTTP API Local Testing")
    print("=" * 60)
    
    # Test 1: Direct API call
    print("\n1️⃣ Testing Direct API Call")
    direct_success = test_direct_api_call()
    
    if not direct_success:
        print("\n❌ Direct API call failed. Please check your credentials.")
        return
    
    # Test 2: Client initialization
    print("\n2️⃣ Testing Client Initialization")
    zendesk_client = test_zendesk_authentication()
    
    if not zendesk_client:
        print("\n❌ Client initialization failed.")
        return
    
    # Test 3: Mock ticket creation
    print("\n3️⃣ Testing Mock Ticket Creation")
    ticket_result = await test_mock_ticket_creation(zendesk_client)
    
    if ticket_result:
        print(f"\n🎉 All tests passed successfully!")
        print(f"   ✅ Direct API authentication working")
        print(f"   ✅ Client initialization working")
        print(f"   ✅ Mock ticket creation working")
        print(f"\n🔗 Created ticket: {ticket_result.get('ticket_url')}")
    else:
        print(f"\n⚠️ Some tests failed. Check the logs above.")

if __name__ == "__main__":
    asyncio.run(main())
