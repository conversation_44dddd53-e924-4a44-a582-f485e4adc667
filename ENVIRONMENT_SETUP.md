# 🔧 Environment Setup Guide

## 🚨 **QUICK FIX FOR STARTUP ERROR**

Create a `.env` file in your project root with these **REQUIRED** variables:

```bash
# Create .env file
touch .env
```

Add these to your `.env` file:

```bash
# ============================================================================
# 🤖 AI MODEL CONFIGURATION (REQUIRED)
# ============================================================================

OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ============================================================================
# 📧 EMAIL CONFIGURATION (REQUIRED) 
# ============================================================================

EMAIL=<EMAIL>
CLAIMS_EMAIL_PASSWORD=your_gmail_app_password_here

# ============================================================================
# 🎫 ZENDESK CONFIGURATION (REQUIRED)
# ============================================================================

ZENDESK_SUBDOMAIN=your_subdomain
ZENDESK_EMAIL=<EMAIL>
ZENDESK_API_TOKEN=your_zendesk_api_token_here

# ============================================================================
# 🗄️ SUPABASE CONFIGURATION (REQUIRED)
# ============================================================================

SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# ============================================================================
# 🤝 HUMANLAYER CONFIGURATION (OPTIONAL)
# ============================================================================

HUMANLAYER_API_KEY=your_humanlayer_api_key_here
HUMANLAYER_WEBHOOK_SECRET=your_humanlayer_webhook_secret_here
```

## 🚀 **RESTART YOUR APPLICATION**

After setting the environment variables:

```bash
# Stop the current container
docker-compose down

# Start with new environment variables
docker-compose up
```

## 📋 **WHERE TO GET API KEYS**

### **OpenAI API Key**
1. Go to: https://platform.openai.com/api-keys
2. Create new secret key
3. Copy and paste into .env file

### **Anthropic API Key** 
1. Go to: https://console.anthropic.com/
2. Create API key
3. Copy and paste into .env file

### **Gmail App Password**
1. Enable 2-factor authentication on Gmail
2. Go to: https://support.google.com/accounts/answer/185833
3. Generate app password for "Mail"
4. Use this password (not your regular Gmail password)

### **Zendesk API Token**
1. Go to Zendesk Admin Center
2. Apps and integrations -> APIs -> Zendesk API
3. Generate new API token
4. Copy token and your subdomain

### **Supabase Keys**
1. Go to your Supabase project dashboard
2. Settings -> API
3. Copy Project URL, anon key, and service_role key

## ✅ **VERIFICATION**

After setting all variables, your application should start successfully:

```bash
docker-compose up
```

You should see:
```
INFO: Uvicorn running on http://0.0.0.0:8000
[INFO] 🚀 [STARTUP] Starting Zurich AI Claims Processing System...
[INFO] ✅ [STARTUP] Application started successfully
```

## 🔐 **SECURITY NOTES**

- **Never commit .env file** to version control
- **Use app passwords** for Gmail (not regular password)  
- **Rotate keys regularly** in production
- **Use different keys** for development/production

## 🆘 **TROUBLESHOOTING**

### Error: "Field required"
- Check that all REQUIRED fields are set in .env file
- Ensure no typos in variable names
- Check that .env file is in project root

### Error: "Invalid API key"
- Verify API keys are correct and active
- Check for extra spaces or newlines
- Ensure keys have proper permissions

### Error: "Connection failed"
- Verify network connectivity
- Check API endpoints are accessible
- Confirm credentials are valid 