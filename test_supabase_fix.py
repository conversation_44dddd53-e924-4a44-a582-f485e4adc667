#!/usr/bin/env python3

import os
import sys
from datetime import datetime
import uuid

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database.supabase_client import SupabaseClient
from config.settings import Settings

def test_supabase_operations():
    """Test all Supabase database operations with service role key"""
    print("🧪 Testing Supabase Operations with Service Role Key")
    print("=" * 60)
    
    try:
        # Initialize settings and client
        settings = Settings()
        print(f"✅ Settings loaded")
        print(f"📊 Supabase URL: {settings.supabase_url}")
        print(f"🔑 Service Role Key Length: {len(settings.supabase_service_role_key)} characters")
        print(f"🔑 Service Role Key Preview: {settings.supabase_service_role_key[:10]}...")
        
        # Initialize Supabase client
        supabase_client = SupabaseClient()
        print(f"✅ Supabase client initialized")
        
        # Test 1: Check RLS status for all tables
        print("\n🔍 Testing RLS Status for All Tables:")
        print("-" * 40)
        
        tables_to_check = ['claims', 'zendesk_tickets', 'attachments', 'claim_history']
        
        for table_name in tables_to_check:
            try:
                # Try to query the table directly
                result = supabase_client.client.table(table_name).select("*").limit(1).execute()
                print(f"✅ {table_name}: Accessible (RLS bypassed)")
            except Exception as e:
                print(f"❌ {table_name}: Permission denied - {str(e)}")
        
        # Test 2: Test zendesk_tickets table specifically
        print("\n🎫 Testing zendesk_tickets Table Operations:")
        print("-" * 40)
        
        test_ticket_data = {
            'ticket_id': '9999',
            'workflow_id': f'test_{uuid.uuid4()}',
            'claim_id': f'claim_{uuid.uuid4()}',
            'subject': 'Test Ticket for RLS Fix',
            'description': 'Testing service role key permissions',
            'status': 'new',
            'priority': 'normal',
            'requester_email': '<EMAIL>',
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }
        
        try:
            # Try to insert a test record
            result = supabase_client.client.table('zendesk_tickets').insert(test_ticket_data).execute()
            print(f"✅ zendesk_tickets INSERT: Success")
            
            # Try to read it back
            read_result = supabase_client.client.table('zendesk_tickets').select("*").eq('ticket_id', '9999').execute()
            print(f"✅ zendesk_tickets SELECT: Success - Found {len(read_result.data)} records")
            
            # Clean up - delete the test record
            delete_result = supabase_client.client.table('zendesk_tickets').delete().eq('ticket_id', '9999').execute()
            print(f"✅ zendesk_tickets DELETE: Success")
            
        except Exception as e:
            print(f"❌ zendesk_tickets operations failed: {str(e)}")
            print(f"🔧 This indicates RLS is still blocking service role access")
        
        # Test 3: Test claims table
        print("\n📋 Testing claims Table Operations:")
        print("-" * 40)
        
        test_claim_data = {
            'claim_id': f'test_claim_{uuid.uuid4()}',
            'workflow_id': f'test_workflow_{uuid.uuid4()}',
            'email_content': 'Test claim for RLS verification',
            'sender_email': '<EMAIL>',
            'subject': 'Test Claim',
            'claim_type': 'AUTO',
            'status': 'PENDING',
            'confidence': 0.95,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }
        
        try:
            # Try to insert a test claim
            result = supabase_client.client.table('claims').insert(test_claim_data).execute()
            print(f"✅ claims INSERT: Success")
            
            # Clean up
            delete_result = supabase_client.client.table('claims').delete().eq('claim_id', test_claim_data['claim_id']).execute()
            print(f"✅ claims DELETE: Success")
            
        except Exception as e:
            print(f"❌ claims operations failed: {str(e)}")
        
        print("\n🎯 CRITICAL FIX NEEDED:")
        print("-" * 40)
        print("The logs show 'permission denied for table zendesk_tickets'")
        print("This means RLS is still enabled. Run this SQL in Supabase SQL Editor:")
        print()
        print("-- 1. Disable RLS on all tables")
        print("ALTER TABLE claims DISABLE ROW LEVEL SECURITY;")
        print("ALTER TABLE zendesk_tickets DISABLE ROW LEVEL SECURITY;")
        print("ALTER TABLE attachments DISABLE ROW LEVEL SECURITY;")
        print("ALTER TABLE claim_history DISABLE ROW LEVEL SECURITY;")
        print()
        print("-- 2. Grant full access to service role")
        print("GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;")
        print("GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;")
        print("GRANT USAGE ON SCHEMA public TO service_role;")
        print()
        print("-- 3. Verify RLS is disabled")
        print("SELECT schemaname, tablename, rowsecurity")
        print("FROM pg_tables")
        print("WHERE schemaname = 'public'")
        print("AND tablename IN ('claims', 'zendesk_tickets', 'attachments', 'claim_history');")
        print()
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_supabase_operations()
    if success:
        print("\n✅ Supabase test completed")
    else:
        print("\n❌ Supabase test failed")
        sys.exit(1)
