-- 🔧 Fix Supabase RLS Policies for Service Role Access
-- Run this in Supabase SQL Editor to fix permission issues

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Enable all access for service role" ON claims;
DROP POLICY IF EXISTS "Enable all access for service role" ON zendesk_tickets;
DROP POLICY IF EXISTS "Enable all access for service role" ON attachments;
DROP POLICY IF EXISTS "Enable all access for service role" ON claim_history;

-- Create proper policies for service role access
-- Note: Replace 'service_role' with your actual service role if different

-- Claims table policies
CREATE POLICY "Allow service role full access to claims" ON claims
    FOR ALL 
    TO service_role
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow anon read access to claims" ON claims
    FOR SELECT 
    TO anon
    USING (true);

-- Zendesk tickets table policies  
CREATE POLICY "Allow service role full access to zendesk_tickets" ON zendesk_tickets
    FOR ALL 
    TO service_role
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow anon read access to zendesk_tickets" ON zendesk_tickets
    FOR SELECT 
    TO anon
    USING (true);

-- Attachments table policies
CREATE POLICY "Allow service role full access to attachments" ON attachments
    FOR ALL 
    TO service_role
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow anon read access to attachments" ON attachments
    FOR SELECT 
    TO anon
    USING (true);

-- Claim history table policies
CREATE POLICY "Allow service role full access to claim_history" ON claim_history
    FOR ALL 
    TO service_role
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow anon read access to claim_history" ON claim_history
    FOR SELECT 
    TO anon
    USING (true);

-- Alternative: Disable RLS temporarily for testing (ONLY for development)
-- Uncomment these lines if the above policies don't work:

-- ALTER TABLE claims DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE zendesk_tickets DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE attachments DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE claim_history DISABLE ROW LEVEL SECURITY;

-- Test the setup
SELECT 'Testing table access...' as status;

-- Try to insert a test record
INSERT INTO claims (
    workflow_id,
    sender_email,
    subject,
    body,
    email_type,
    confidence,
    claim_type,
    urgency_level,
    status
) VALUES (
    'policy_test_' || extract(epoch from now()),
    '<EMAIL>',
    'Policy Test',
    'Testing RLS policies',
    'CLAIM_SUBMISSION',
    0.99,
    'AUTO',
    'LOW',
    'POLICY_TEST'
) ON CONFLICT (workflow_id) DO NOTHING;

-- Show current record count
SELECT 
    'claims' as table_name, 
    count(*) as record_count 
FROM claims;
