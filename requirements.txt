# Core AI and ML Libraries
# anthropic>=0.25.0  # Commented out - using GPT-4o instead of <PERSON>>=1.30.0
numpy>=1.24.0
scikit-learn>=1.3.0
pydantic-settings>=2.0.0

# Removed BERT dependencies - using simplified dual GPT-4o approach

# # Document Processing and OCR
# opencv-python>=4.8.0
# pytesseract>=0.3.10
# pdf2image>=1.16.3
# Pillow>=10.0.0
# azure-cognitiveservices-vision-computervision>=0.9.0
# google-cloud-vision>=3.4.0
# boto3>=1.28.0  # For AWS Textract
# paddlepaddle>=2.5.0
# paddleocr>=2.7.0

# Email and Communication
imaplib2>=0.57
email-validator>=2.0.0
jinja2>=3.1.0

# API Integrations
requests>=2.31.0
httpx>=0.24.0
zendesk>=1.1.1
humanlayer>=0.1.0

# Data Processing
pandas>=2.0.0
pydantic>=2.0.0
python-dotenv>=1.0.0
asyncio-mqtt>=0.13.0

# Database and Storage
sqlalchemy>=2.0.0
alembic>=1.12.0
redis>=4.6.0
psycopg2-binary>=2.9.0

# Supabase Integration
supabase>=2.3.0

# Zendesk API Integration (using direct HTTP requests)
# zenpy>=2.0.25  # Removed - using direct HTTP API instead

# Web Framework and API
fastapi>=0.100.0
uvicorn>=0.23.0
starlette>=0.27.0

# Monitoring and Logging
prometheus-client>=0.17.0
structlog>=23.1.0
sentry-sdk>=1.30.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
httpx>=0.24.0  # For testing API calls

# Development Tools
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.3.0

# BAML for structured AI outputs
baml-py>=0.45.0

# Additional utilities
python-multipart>=0.0.6
aiofiles>=23.2.0
python-jose>=3.3.0
passlib>=1.7.4
cryptography>=41.0.0

# Performance
ujson>=5.8.0
orjson>=3.9.0

# NLP for Legal Framework
spacy>=3.6.0
nltk>=3.8.0

# Testing Coverage
pytest-cov>=4.1.0
