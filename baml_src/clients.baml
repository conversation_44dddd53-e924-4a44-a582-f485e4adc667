// 🔧 BAML Client Configuration for Zurich AI Claims Processing
// Defines LLM clients for multi-model consensus engine

// =============================================================================
// 🔄 RETRY POLICIES
// =============================================================================

retry_policy StandardRetry {
  max_retries 3
  strategy {
    type exponential_backoff
    delay_ms 200
    multiplier 1.5
    max_delay_ms 10000
  }
}

retry_policy FastRetry {
  max_retries 2
  strategy {
    type constant_delay
    delay_ms 100
  }
}

// =============================================================================
// 🤖 PRIMARY AI MODELS
// =============================================================================

// GPT-4o - Primary reasoning model
client<llm> GPT4o {
  provider "openai"
  options {
    model "gpt-4o"
    api_key env.OPENAI_API_KEY
    max_tokens 4000
    temperature 0.1  // Low temperature for consistent analysis
  }
}

// Claude 3.5 Sonnet - Backup model (if needed)
client<llm> ClaudeSonnet {
  provider "anthropic"
  options {
    model "claude-3-5-sonnet-20241022"
    api_key env.ANTHROPIC_API_KEY
    max_tokens 4000
    temperature 0.1  // Low temperature for consistent analysis
  }
}

// GPT-4o Mini - Fast classification for simple cases
client<llm> GPT4oMini {
  provider "openai"
  options {
    model "gpt-4o-mini"
    api_key env.OPENAI_API_KEY
    max_tokens 2000
    temperature 0.1
  }
}

// =============================================================================
// 🏛️ SPECIALIZED LEGAL MODELS (via HuggingFace)
// =============================================================================

// Legal-BERT for legal document analysis
client<llm> LegalBERT {
  provider "openai-generic"
  options {
    base_url "https://api-inference.huggingface.co/models/nlpaueb/legal-bert-base-uncased"
    api_key env.HUGGINGFACE_API_KEY
    model "nlpaueb/legal-bert-base-uncased"
    max_tokens 1000
    temperature 0.0  // Deterministic for legal analysis
  }
}

// RiskBERT for risk assessment
client<llm> RiskBERT {
  provider "openai-generic"
  options {
    base_url "https://api-inference.huggingface.co/models/ProsusAI/finbert"
    api_key env.HUGGINGFACE_API_KEY
    model "ProsusAI/finbert"
    max_tokens 1000
    temperature 0.0
  }
}

// FinBERT for financial analysis
client<llm> FinBERT {
  provider "openai-generic"
  options {
    base_url "https://api-inference.huggingface.co/models/ProsusAI/finbert"
    api_key env.HUGGINGFACE_API_KEY
    model "ProsusAI/finbert"
    max_tokens 1000
    temperature 0.0
  }
}

// =============================================================================
// 🔄 FALLBACK AND RETRY STRATEGIES
// =============================================================================

// Primary classification client with fallback
client<llm> PrimaryClassifier {
  provider "fallback"
  options {
    strategy [
      GPT4o,
      GPT4oMini,
      ClaudeSonnet
    ]
  }
}

// High-accuracy consensus client
client<llm> ConsensusEngine {
  provider "round-robin"
  options {
    strategy [
      GPT4o,
      GPT4oMini
    ]
  }
}

// =============================================================================
// 🚀 PERFORMANCE-OPTIMIZED CLIENTS
// =============================================================================

// Fast client for simple classifications
client<llm> FastClassifier {
  provider "openai"
  options {
    model "gpt-4o-mini"
    api_key env.OPENAI_API_KEY
    max_tokens 1000
    temperature 0.0
    timeout_ms 5000  // 5 second timeout for fast responses
  }
}

// High-accuracy client for complex analysis
client<llm> DeepAnalyzer {
  provider "openai"
  options {
    model "gpt-4o"
    api_key env.OPENAI_API_KEY
    max_tokens 8000
    temperature 0.05
    timeout_ms 30000  // 30 second timeout for complex analysis
  }
}

// =============================================================================
// 🔒 SECURE CLIENTS FOR SENSITIVE DATA
// =============================================================================

// Secure client for PII and sensitive information
client<llm> SecureProcessor {
  provider "openai"
  options {
    model "gpt-4o"
    api_key env.OPENAI_API_KEY
    max_tokens 4000
    temperature 0.0  // Deterministic for sensitive data
    // Additional security headers can be added here
  }
}

// =============================================================================
// 🇨🇦 CANADIAN LEGAL SPECIALIST CLIENT
// =============================================================================

// Specialized client for Canadian legal analysis
client<llm> CanadianLegalExpert {
  provider "openai"
  options {
    model "gpt-4o"
    api_key env.OPENAI_API_KEY
    max_tokens 6000
    temperature 0.1
    // Optimized for Canadian legal context
  }
}

// =============================================================================
// 🎯 WORKFLOW-SPECIFIC CLIENTS
// =============================================================================

// Email processing client
client<llm> EmailProcessor {
  provider "fallback"
  retry_policy StandardRetry
  options {
    strategy [
      GPT4o,
      GPT4oMini
    ]
  }
}

// Document analysis client
client<llm> DocumentAnalyzer {
  provider "openai"
  options {
    model "gpt-4o"
    api_key env.OPENAI_API_KEY
    max_tokens 8000  // Large context for documents
    temperature 0.1
  }
}

// Policy verification client
client<llm> PolicyVerifier {
  provider "openai"
  options {
    model "gpt-4o"
    api_key env.OPENAI_API_KEY
    max_tokens 4000
    temperature 0.0  // Deterministic for policy checks
  }
}

// Financial assessment client
client<llm> FinancialAnalyzer {
  provider "round-robin"
  options {
    strategy [
      GPT4o,
      FinBERT
    ]
  }
}

// =============================================================================
// 🧪 TESTING AND DEVELOPMENT CLIENTS
// =============================================================================

// Test client for development
client<llm> TestClient {
  provider "openai"
  options {
    model "gpt-4o-mini"
    api_key env.TEST_OPENAI_API_KEY
    max_tokens 1000
    temperature 0.5
  }
}

// Mock client for unit testing
client<llm> MockClient {
  provider "openai"
  options {
    model "gpt-4o-mini"
    api_key "test-key"
    max_tokens 500
    temperature 0.0
    base_url "http://localhost:8080/mock"  // Mock server for testing
  }
}
