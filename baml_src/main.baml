// 🏆 Zurich AI Claims Processing - BAML Schemas
// Structured AI outputs for email classification, claim detection, and document analysis

// =============================================================================
// 🎯 EMAIL CLASSIFICATION SCHEMAS
// =============================================================================

enum EmailType {
  CLAIM_SUBMISSION
  CLAIM_INQUIRY
  POLICY_QUESTION
  BILLING_INQUIRY
  GENERAL_INQUIRY
  SPAM
  NOT_INSURANCE_RELATED
}

enum ClaimType {
  AUTO_ACCIDENT
  PROPERTY_DAMAGE
  LIABILITY
  PERSONAL_INJURY
  THEFT
  FIRE_DAMAGE
  WATER_DAMAGE
  VANDALISM
  OTHER
}

enum UrgencyLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ConfidenceLevel {
  VERY_LOW    // 0-20%
  LOW         // 21-40%
  MEDIUM      // 41-60%
  HIGH        // 61-80%
  VERY_HIGH   // 81-100%
}

// =============================================================================
// 📧 EMAIL ANALYSIS RESULT
// =============================================================================

class EmailAnalysis {
  email_type EmailType @description("Primary classification of the email")
  is_claim bool @description("True if this email contains a claim submission or inquiry")
  claim_type ClaimType? @description("Type of claim if is_claim is true")
  urgency_level UrgencyLevel @description("Urgency level based on content analysis")
  confidence ConfidenceLevel @description("AI confidence in the classification")
  
  // Key information extraction
  policy_number string? @description("Insurance policy number if mentioned")
  claim_number string? @description("Existing claim number if referenced")
  incident_date string? @description("Date of incident in ISO format if mentioned")
  location string? @description("Location of incident if mentioned")
  
  // Contact information
  customer_name string? @description("Customer name extracted from email")
  customer_phone string? @description("Phone number if provided")
  customer_email string @description("Email address of sender")
  
  // Content analysis
  summary string @description("Brief summary of email content (max 200 chars)")
  key_details string[] @description("Important details extracted from email")
  attachments_mentioned bool @description("True if email mentions attachments")
  
  // Decision flags
  requires_human_review bool @description("True if human review is recommended")
  requires_immediate_action bool @description("True if immediate action is needed")
  
  reasoning string @description("Explanation of classification decision")
}

// =============================================================================
// 📄 DOCUMENT ANALYSIS SCHEMAS
// =============================================================================

enum DocumentType {
  POLICE_REPORT
  MEDICAL_REPORT
  REPAIR_ESTIMATE
  PHOTO_EVIDENCE
  INSURANCE_FORM
  RECEIPT
  INVOICE
  WITNESS_STATEMENT
  CORRESPONDENCE
  OTHER_DOCUMENT
}

class DocumentAnalysis {
  document_type DocumentType @description("Type of document identified")
  confidence ConfidenceLevel @description("Confidence in document type classification")
  
  // Extracted information
  key_information string[] @description("Important information extracted from document")
  dates_mentioned string[] @description("Dates found in document (ISO format)")
  amounts_mentioned float[] @description("Monetary amounts found in document")
  
  // Quality assessment
  is_readable bool @description("True if document text is clearly readable")
  quality_score int @description("Document quality score 1-10")
  
  // Relevance
  is_relevant_to_claim bool @description("True if document is relevant to insurance claim")
  relevance_explanation string @description("Explanation of document relevance")
}

// =============================================================================
// 🔍 POLICY VERIFICATION SCHEMAS
// =============================================================================

enum CoverageStatus {
  COVERED
  NOT_COVERED
  PARTIALLY_COVERED
  REQUIRES_INVESTIGATION
  POLICY_NOT_FOUND
}

class PolicyVerification {
  policy_found bool @description("True if policy was found in system")
  policy_active bool @description("True if policy is currently active")
  coverage_status CoverageStatus @description("Coverage status for the claim")
  
  // Policy details
  policy_type string? @description("Type of insurance policy")
  coverage_limits float? @description("Coverage limit amount")
  deductible float? @description("Policy deductible amount")
  
  // Verification details
  verification_notes string @description("Notes about policy verification")
  exclusions_apply bool @description("True if policy exclusions may apply")
  exclusion_details string[] @description("Details of applicable exclusions")
}

// =============================================================================
// ⚖️ LEGAL ASSESSMENT SCHEMAS
// =============================================================================

enum LegalRisk {
  VERY_LOW
  LOW
  MEDIUM
  HIGH
  VERY_HIGH
}

enum LiabilityAssessment {
  CLEAR_LIABILITY
  DISPUTED_LIABILITY
  NO_LIABILITY
  REQUIRES_INVESTIGATION
}

class LegalAnalysis {
  legal_risk LegalRisk @description("Overall legal risk assessment")
  liability_assessment LiabilityAssessment @description("Liability determination")
  
  // Canadian legal considerations
  applicable_province string? @description("Canadian province where incident occurred")
  relevant_statutes string[] @description("Relevant Canadian statutes or regulations")
  precedent_cases string[] @description("Similar legal precedents if applicable")
  
  // Risk factors
  risk_factors string[] @description("Identified legal risk factors")
  mitigation_strategies string[] @description("Recommended risk mitigation strategies")
  
  legal_notes string @description("Additional legal considerations")
}

// =============================================================================
// 💰 FINANCIAL ASSESSMENT SCHEMAS
// =============================================================================

class FinancialAssessment {
  estimated_claim_value float @description("Estimated total claim value in CAD")
  confidence_range_min float @description("Minimum estimated value")
  confidence_range_max float @description("Maximum estimated value")
  
  // Cost breakdown
  property_damage_estimate float? @description("Estimated property damage cost")
  medical_costs_estimate float? @description("Estimated medical costs")
  legal_costs_estimate float? @description("Estimated legal costs")
  other_costs_estimate float? @description("Other estimated costs")
  
  // Financial flags
  exceeds_authority_limit bool @description("True if claim exceeds adjuster authority")
  requires_reserves bool @description("True if financial reserves should be set")
  
  financial_notes string @description("Additional financial considerations")
}

// =============================================================================
// 🎯 FINAL DECISION SCHEMAS
// =============================================================================

enum ClaimDecision {
  APPROVE_PAYMENT
  DENY_CLAIM
  REQUIRES_INVESTIGATION
  REQUIRES_HUMAN_APPROVAL
  REQUEST_MORE_INFORMATION
}

enum NextAction {
  CREATE_ZENDESK_TICKET
  SCHEDULE_INSPECTION
  REQUEST_DOCUMENTS
  CONTACT_CUSTOMER
  ESCALATE_TO_MANAGER
  CLOSE_INQUIRY
  TRANSFER_TO_SPECIALIST
}

class ClaimRecommendation {
  decision ClaimDecision @description("Recommended claim decision")
  next_actions NextAction[] @description("Recommended next actions")
  
  // Priority and timing
  priority_level UrgencyLevel @description("Processing priority level")
  target_response_time int @description("Target response time in hours")
  
  // Human involvement
  requires_human_approval bool @description("True if human approval is required")
  approval_reason string? @description("Reason why human approval is needed")
  recommended_approver string? @description("Recommended person/role for approval")
  
  // Communication
  customer_communication_required bool @description("True if customer should be contacted")
  communication_template string? @description("Suggested communication template")
  
  // Workflow
  zendesk_priority string @description("Suggested Zendesk ticket priority")
  zendesk_tags string[] @description("Suggested Zendesk tags")
  
  recommendation_summary string @description("Summary of recommendation and reasoning")
}

// =============================================================================
// 🔄 MULTI-MODEL CONSENSUS SCHEMA
// =============================================================================

class ModelConsensus {
  claude_analysis EmailAnalysis @description("Analysis from Claude 3.5 Sonnet")
  gpt4_analysis EmailAnalysis @description("Analysis from GPT-4o")
  
  // Consensus results
  consensus_reached bool @description("True if models agree on classification")
  consensus_confidence ConfidenceLevel @description("Confidence in consensus")
  
  // Disagreement handling
  disagreement_areas string[] @description("Areas where models disagree")
  tie_breaker_needed bool @description("True if human tie-breaker is needed")
  
  // Final consensus
  final_analysis EmailAnalysis @description("Final consensus analysis")
  consensus_notes string @description("Notes about consensus process")
}

// =============================================================================
// 🤖 BAML FUNCTIONS FOR AI ANALYSIS
// =============================================================================

// Primary email classification function using GPT-4o
function ClassifyEmail(
  email_subject: string,
  email_body: string,
  sender_email: string,
  attachments: string[]
) -> EmailAnalysis {
  client GPT4o
  prompt #"
    You are an expert insurance claims analyst for Zurich Insurance, specializing in Canadian insurance law and claims processing.
    You are powered by GPT-4o and provide highly accurate email classification and analysis.

    Analyze the following email and provide a comprehensive classification and analysis.

    {{ ctx.output_format }}

    EMAIL DETAILS:
    From: {{ sender_email }}
    Subject: {{ email_subject }}
    Attachments: {{ attachments | join(", ") if attachments else "None" }}

    Body:
    {{ email_body }}

    ANALYSIS GUIDELINES:

    1. EMAIL CLASSIFICATION:
       - Determine if this is a claim submission, inquiry, or other type
       - Assess urgency based on content (keywords like "urgent", "emergency", injury mentions)
       - Look for policy/claim numbers, dates, locations

    2. CLAIM DETECTION:
       - Look for incident descriptions, damage reports, injury mentions
       - Identify claim types: auto, property, liability, etc.
       - Extract incident dates and locations

    3. INFORMATION EXTRACTION:
       - Extract policy numbers (format: various patterns)
       - Extract claim numbers if referenced
       - Extract customer contact information
       - Identify key details and dates

    4. DECISION FLAGS:
       - Requires human review: complex cases, high values, legal issues
       - Immediate action: injuries, emergencies, time-sensitive matters

    5. CONFIDENCE ASSESSMENT:
       - VERY_HIGH (81-100%): Clear, unambiguous classification
       - HIGH (61-80%): Strong indicators, minor ambiguity
       - MEDIUM (41-60%): Some uncertainty, mixed signals
       - LOW (21-40%): Weak indicators, significant ambiguity
       - VERY_LOW (0-20%): Very unclear, insufficient information

    Provide detailed reasoning for your classification decisions.
  "#
}

// Validation function using GPT-4o Mini for consensus
function ValidateEmailClassification(
  email_subject: string,
  email_body: string,
  sender_email: string,
  attachments: string[]
) -> EmailAnalysis {
  client GPT4oMini
  prompt #"
    You are a senior insurance claims validator for Zurich Insurance, providing second-opinion analysis on email classifications.
    You are powered by GPT-4o Mini for fast and efficient validation.

    Analyze this email independently and provide your classification.

    {{ ctx.output_format }}

    EMAIL DETAILS:
    From: {{ sender_email }}
    Subject: {{ email_subject }}
    Attachments: {{ attachments | join(", ") if attachments else "None" }}

    Body:
    {{ email_body }}

    VALIDATION FOCUS:

    1. Independent classification without bias
    2. Focus on Canadian insurance context
    3. Identify any red flags or unusual patterns
    4. Assess information completeness
    5. Evaluate urgency and priority levels

    Provide thorough analysis with clear reasoning.
  "#
}

// Document analysis function
function AnalyzeDocument(
  document_text: string,
  document_filename: string,
  claim_context: string
) -> DocumentAnalysis {
  client GPT4o
  prompt #"
    You are a document analysis specialist for insurance claims processing.

    Analyze the following document in the context of an insurance claim.

    {{ ctx.output_format }}

    DOCUMENT DETAILS:
    Filename: {{ document_filename }}
    Claim Context: {{ claim_context }}

    Document Content:
    {{ document_text }}

    ANALYSIS REQUIREMENTS:

    1. Document Type Classification:
       - Identify the type of document (police report, medical report, etc.)
       - Assess document authenticity indicators

    2. Information Extraction:
       - Extract key facts, dates, amounts
       - Identify relevant parties and contacts
       - Note any inconsistencies or concerns

    3. Quality Assessment:
       - Evaluate readability and completeness
       - Rate document quality (1-10 scale)
       - Identify missing information

    4. Claim Relevance:
       - Determine relevance to insurance claim
       - Identify supporting or contradicting evidence
       - Note any red flags or concerns

    Provide detailed analysis with specific examples from the document.
  "#
}

// Policy verification function
function VerifyPolicyDetails(
  policy_number: string,
  incident_date: string,
  claim_type: string,
  policy_data: string
) -> PolicyVerification {
  client PolicyVerifier
  prompt #"
    You are a policy verification specialist for Zurich Insurance.

    Verify policy coverage for the given claim details.

    {{ ctx.output_format }}

    VERIFICATION REQUEST:
    Policy Number: {{ policy_number }}
    Incident Date: {{ incident_date }}
    Claim Type: {{ claim_type }}

    Policy Data:
    {{ policy_data }}

    VERIFICATION CHECKLIST:

    1. Policy Status:
       - Confirm policy is active on incident date
       - Check for any lapses or cancellations
       - Verify premium payment status

    2. Coverage Analysis:
       - Determine if claim type is covered
       - Check coverage limits and deductibles
       - Identify any applicable exclusions

    3. Exclusion Review:
       - Review policy exclusions
       - Assess if any exclusions apply to this claim
       - Note any grey areas requiring investigation

    4. Special Conditions:
       - Check for any special policy conditions
       - Review endorsements or riders
       - Note any coverage modifications

    Provide clear coverage determination with supporting details.
  "#
}

// Legal risk assessment function
function AssessLegalRisk(
  incident_description: string,
  location: string,
  parties_involved: string,
  claim_value: float
) -> LegalAnalysis {
  client GPT4o
  prompt #"
    You are a legal risk analyst specializing in Canadian insurance law.

    Assess the legal risk and liability for this insurance claim.

    {{ ctx.output_format }}

    CLAIM DETAILS:
    Incident: {{ incident_description }}
    Location: {{ location }}
    Parties: {{ parties_involved }}
    Estimated Value: ${{ claim_value }} CAD

    LEGAL ANALYSIS FRAMEWORK:

    1. Liability Assessment:
       - Determine clear, disputed, or no liability
       - Identify contributing factors
       - Assess comparative negligence

    2. Canadian Legal Context:
       - Identify applicable provincial laws
       - Reference relevant statutes and regulations
       - Consider tort law principles

    3. Risk Factors:
       - Identify potential legal complications
       - Assess litigation probability
       - Evaluate settlement vs. defense strategy

    4. Precedent Analysis:
       - Reference similar Canadian cases
       - Note relevant legal precedents
       - Consider recent legal developments

    5. Risk Mitigation:
       - Recommend risk mitigation strategies
       - Suggest early intervention opportunities
       - Identify expert witness needs

    Provide comprehensive legal risk assessment with Canadian law focus.
  "#
}
