// 🧪 BAML Test Cases for Zurich AI Claims Processing
// Comprehensive test suite for email classification and analysis functions

// =============================================================================
// 📧 EMAIL CLASSIFICATION TESTS
// =============================================================================

test ClaimSubmissionTest {
  functions [ClassifyEmail]
  args {
    email_subject "Car Accident Claim - Policy #ZUR123456"
    email_body "Hi, I was involved in a car accident yesterday at the intersection of King St and Queen St in Toronto. The other driver ran a red light and hit my vehicle. My car has significant damage to the front end and I need to file a claim. My policy number is ZUR123456. Please let me know what documents you need. Thanks, <PERSON>"
    sender_email "<EMAIL>"
    attachments ["accident_photos.jpg", "police_report.pdf"]
  }
}

test PolicyInquiryTest {
  functions [ClassifyEmail]
  args {
    email_subject "Question about my coverage"
    email_body "Hello, I have a question about what's covered under my home insurance policy. Specifically, I want to know if water damage from a burst pipe would be covered. My policy number is ZUR789012. Thank you."
    sender_email "<EMAIL>"
    attachments []
  }
}

test SpamEmailTest {
  functions [ClassifyEmail]
  args {
    email_subject "URGENT: You've won $1,000,000!!!"
    email_body "Congratulations! You have been selected to receive $1,000,000 in our lottery. Click here to claim your prize now! This offer expires in 24 hours."
    sender_email "<EMAIL>"
    attachments []
  }
}

test UrgentClaimTest {
  functions [ClassifyEmail]
  args {
    email_subject "URGENT - House Fire Claim"
    email_body "URGENT: Our house caught fire last night and we lost everything. We need immediate assistance. The fire department said it was caused by electrical issues. We have nowhere to stay and need help with temporary accommodation. Policy: ZUR456789. Please call me immediately at ************."
    sender_email "<EMAIL>"
    attachments ["fire_damage_photos.zip", "fire_department_report.pdf"]
  }
}

test LiabilityClaimTest {
  functions [ClassifyEmail]
  args {
    email_subject "Liability Claim - Slip and Fall"
    email_body "I am writing to report an incident that occurred at my business premises. A customer slipped and fell on wet floors and is claiming injury. They are threatening to sue. I need to file a liability claim under my business insurance. Policy number: ZUR321654. The incident happened on June 25, 2025 at approximately 2:30 PM."
    sender_email "<EMAIL>"
    attachments ["incident_report.pdf", "witness_statements.docx"]
  }
}

test IncompleteClaimTest {
  functions [ClassifyEmail]
  args {
    email_subject "Claim"
    email_body "I need to file a claim. Something happened to my car."
    sender_email "<EMAIL>"
    attachments []
  }
}

// =============================================================================
// 🔄 CONSENSUS VALIDATION TESTS
// =============================================================================

test ConsensusValidationTest {
  functions [ValidateEmailClassification]
  args {
    email_subject "Auto Accident - Need Help"
    email_body "Hi, I was in a car accident this morning. The other driver was texting and rear-ended me at a red light. My neck hurts and my car is damaged. I think I need to go to the hospital. My policy is ZUR555777. What should I do?"
    sender_email "<EMAIL>"
    attachments ["accident_scene.jpg"]
  }
}

// =============================================================================
// 📄 DOCUMENT ANALYSIS TESTS
// =============================================================================

test PoliceReportAnalysisTest {
  functions [AnalyzeDocument]
  args {
    document_text "POLICE REPORT - MOTOR VEHICLE ACCIDENT\nReport Number: TR-2025-001234\nDate: June 28, 2025\nTime: 14:30\nLocation: King St W & Queen St, Toronto, ON\n\nVehicle 1: 2020 Honda Civic, License: ABCD123\nDriver: John Smith, DOB: 1985-03-15\nInsurance: Zurich Insurance, Policy: ZUR123456\n\nVehicle 2: 2018 Ford F-150, License: XYZ789\nDriver: Mike Johnson, DOB: 1978-11-22\nInsurance: State Farm, Policy: SF987654\n\nDescription: Vehicle 2 failed to stop at red light and collided with Vehicle 1 which was proceeding through intersection on green light. Vehicle 1 sustained damage to front end. No injuries reported at scene.\n\nOfficer: Constable Sarah Wilson, Badge #4567\nCharges: Vehicle 2 driver charged with running red light under HTA Section 144(18)"
    document_filename "police_report_TR-2025-001234.pdf"
    claim_context "Auto accident claim for policy ZUR123456, incident on June 28, 2025"
  }
}

test MedicalReportAnalysisTest {
  functions [AnalyzeDocument]
  args {
    document_text "MEDICAL REPORT\nPatient: John Smith\nDOB: 1985-03-15\nDate of Service: June 28, 2025\n\nChief Complaint: Neck pain following motor vehicle accident\n\nHistory: Patient reports being involved in rear-end collision earlier today. Complains of neck stiffness and headache. No loss of consciousness. Wearing seatbelt at time of impact.\n\nExamination: Alert and oriented. Cervical spine tender to palpation. Range of motion limited due to pain. No neurological deficits noted.\n\nDiagnosis: Cervical strain (whiplash)\n\nTreatment: Prescribed muscle relaxants and physiotherapy. Follow-up in 1 week.\n\nWork Status: Off work for 3 days, then light duties for 2 weeks.\n\nDr. Emily Chen, MD\nToronto General Hospital"
    document_filename "medical_report_john_smith.pdf"
    claim_context "Personal injury component of auto accident claim ZUR123456"
  }
}

// =============================================================================
// 🏛️ POLICY VERIFICATION TESTS
// =============================================================================

test PolicyVerificationActiveTest {
  functions [VerifyPolicyDetails]
  args {
    policy_number "ZUR123456"
    incident_date "2025-06-28"
    claim_type "AUTO_ACCIDENT"
    policy_data "Policy Number: ZUR123456\nPolicyholder: John Smith\nPolicy Type: Auto Insurance\nEffective Date: 2024-01-01\nExpiry Date: 2025-12-31\nPremium Status: Paid\nCoverage: Comprehensive and Collision\nDeductible: $500\nLiability Limit: $1,000,000\nProperty Damage: $500,000\nMedical Payments: $50,000\nUninsured Motorist: $1,000,000"
  }
}

test PolicyVerificationExpiredTest {
  functions [VerifyPolicyDetails]
  args {
    policy_number "ZUR999888"
    incident_date "2025-06-28"
    claim_type "AUTO_ACCIDENT"
    policy_data "Policy Number: ZUR999888\nPolicyholder: Jane Doe\nPolicy Type: Auto Insurance\nEffective Date: 2023-01-01\nExpiry Date: 2024-12-31\nPremium Status: Lapsed\nCoverage: Liability Only\nNote: Policy cancelled for non-payment on 2024-11-15"
  }
}

// =============================================================================
// ⚖️ LEGAL RISK ASSESSMENT TESTS
// =============================================================================

test LegalRiskLowTest {
  functions [AssessLegalRisk]
  args {
    incident_description "Minor fender bender in parking lot. Other driver admitted fault and apologized. No injuries. Minimal property damage."
    location "Toronto, Ontario"
    parties_involved "Insured: John Smith, Third Party: Mike Johnson (admitted fault)"
    claim_value 2500.0
  }
}

test LegalRiskHighTest {
  functions [AssessLegalRisk]
  args {
    incident_description "Multi-vehicle accident on Highway 401. Disputed liability. Serious injuries reported. Potential fatality. Commercial vehicle involved."
    location "Toronto, Ontario"
    parties_involved "Insured: ABC Transport Ltd, Third Parties: Multiple private vehicles, Pedestrian injured"
    claim_value 2500000.0
  }
}

// =============================================================================
// 🇨🇦 CANADIAN LEGAL CONTEXT TESTS
// =============================================================================

test CanadianLegalContextTest {
  functions [AssessLegalRisk]
  args {
    incident_description "Slip and fall at retail store. Customer claims wet floor was not properly marked. Seeking damages for lost wages and medical expenses."
    location "Vancouver, British Columbia"
    parties_involved "Insured: Retail Store Inc, Claimant: Customer with back injury"
    claim_value 75000.0
  }
}

// =============================================================================
// 🔄 EDGE CASE TESTS
// =============================================================================

test EmptyEmailTest {
  functions [ClassifyEmail]
  args {
    email_subject ""
    email_body ""
    sender_email "<EMAIL>"
    attachments []
  }
}

test NonEnglishEmailTest {
  functions [ClassifyEmail]
  args {
    email_subject "Réclamation d'assurance automobile"
    email_body "Bonjour, j'ai eu un accident de voiture hier. Ma police d'assurance est ZUR123456. Pouvez-vous m'aider?"
    sender_email "<EMAIL>"
    attachments []
  }
}

test VeryLongEmailTest {
  functions [ClassifyEmail]
  args {
    email_subject "Detailed Claim Report"
    email_body "This is a very long email with extensive details about an incident that occurred... [This would be a very long email body with multiple paragraphs describing a complex claim scenario with many details, parties involved, timeline of events, etc.]"
    sender_email "<EMAIL>"
    attachments ["document1.pdf", "document2.pdf", "photos.zip", "statements.docx"]
  }
}
