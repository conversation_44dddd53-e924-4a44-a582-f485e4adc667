// 🔧 BAML Generator Configuration for Zurich AI Claims Processing
// Generates Python client code for the claims processing system

generator ZurichClaimsClient {
  output_type "python/pydantic"
  output_dir "../src/baml_client"
  version "0.90.2"
}

// Additional generator for TypeScript (if needed for frontend)
generator ZurichClaimsClientTS {
  output_type "typescript"
  output_dir "../frontend/src/baml_client"
  version "0.90.2"
}
