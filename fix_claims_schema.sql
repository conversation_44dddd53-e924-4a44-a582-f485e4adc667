-- 🔧 CRITICAL CLAIMS & CLAIM_HISTORY TABLE SCHEMA FIX
-- This script adds ALL missing columns that the application code expects
-- Run this script in your Supabase SQL Editor

-- ========================================
-- 1. FIX CLAIMS TABLE - ADD MISSING COLUMNS
-- ========================================

-- Add ALL missing columns to claims table (ALL NULLABLE to prevent insertion errors)
ALTER TABLE claims
ADD COLUMN IF NOT EXISTS email_subject TEXT NULL,
ADD COLUMN IF NOT EXISTS email_body TEXT NULL,
ADD COLUMN IF NOT EXISTS sender_name TEXT NULL,
ADD COLUMN IF NOT EXISTS received_at TIMESTAMP WITH TIME ZONE NULL,
ADD COLUMN IF NOT EXISTS confidence_level TEXT NULL,
ADD COLUMN IF NOT EXISTS ai_classification_result JSONB NULL,
ADD COLUMN IF NOT EXISTS consensus_confidence DECIMAL(5,4) NULL,
ADD COLUMN IF NOT EXISTS policy_number TEXT NULL,
ADD COLUMN IF NOT EXISTS incident_date TIMESTAMP WITH TIME ZONE NULL,
ADD COLUMN IF NOT EXISTS incident_location TEXT NULL,
ADD COLUMN IF NOT EXISTS estimated_value DECIMAL(12,2) NULL,
ADD COLUMN IF NOT EXISTS claimant_name TEXT NULL,
ADD COLUMN IF NOT EXISTS claimant_phone TEXT NULL,
ADD COLUMN IF NOT EXISTS zendesk_ticket_id TEXT NULL,
ADD COLUMN IF NOT EXISTS zendesk_ticket_url TEXT NULL;

-- Migrate existing data from old column names to new ones (if data exists)
UPDATE claims 
SET 
    email_subject = COALESCE(email_subject, subject),
    email_body = COALESCE(email_body, body),
    confidence_level = COALESCE(confidence_level, confidence::text),
    received_at = COALESCE(received_at, created_at)
WHERE email_subject IS NULL OR email_body IS NULL OR confidence_level IS NULL OR received_at IS NULL;

-- ========================================
-- 2. FIX CLAIM_HISTORY TABLE - ADD MISSING COLUMNS
-- ========================================

-- Add missing columns to claim_history table (ALL NULLABLE)
ALTER TABLE claim_history
ADD COLUMN IF NOT EXISTS event_description TEXT NULL,
ADD COLUMN IF NOT EXISTS triggered_by TEXT NULL DEFAULT 'system';

-- Migrate existing data from old column names to new ones
UPDATE claim_history 
SET 
    event_description = COALESCE(event_description, description),
    triggered_by = COALESCE(triggered_by, 'system')
WHERE event_description IS NULL OR triggered_by IS NULL;

-- ========================================
-- 3. CREATE INDEXES FOR NEW COLUMNS
-- ========================================

-- Claims table indexes for new columns
CREATE INDEX IF NOT EXISTS idx_claims_email_subject ON claims(email_subject);
CREATE INDEX IF NOT EXISTS idx_claims_sender_name ON claims(sender_name);
CREATE INDEX IF NOT EXISTS idx_claims_confidence_level ON claims(confidence_level);
CREATE INDEX IF NOT EXISTS idx_claims_policy_number ON claims(policy_number);
CREATE INDEX IF NOT EXISTS idx_claims_claimant_name ON claims(claimant_name);
CREATE INDEX IF NOT EXISTS idx_claims_zendesk_ticket_id ON claims(zendesk_ticket_id);
CREATE INDEX IF NOT EXISTS idx_claims_received_at ON claims(received_at);

-- Claim history table indexes for new columns
CREATE INDEX IF NOT EXISTS idx_claim_history_triggered_by ON claim_history(triggered_by);

-- ========================================
-- 4. VERIFY SCHEMA ALIGNMENT
-- ========================================

-- Show claims table schema to verify all required columns exist
SELECT 'CLAIMS TABLE COLUMNS:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'claims' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Show claim_history table schema to verify all required columns exist
SELECT 'CLAIM_HISTORY TABLE COLUMNS:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'claim_history' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Show record counts
SELECT 
    'claims' as table_name, 
    count(*) as record_count 
FROM claims
UNION ALL
SELECT 
    'claim_history' as table_name, 
    count(*) as record_count 
FROM claim_history;

-- ========================================
-- 5. SCHEMA READY FOR REAL-TIME DATA
-- ========================================

-- Schema is now ready to receive real-time data from:
-- ✅ Email processing workflow
-- ✅ AI classification results
-- ✅ Zendesk ticket creation
-- ✅ Attachment uploads
-- ✅ Claim history tracking

-- No test data inserted - ready for production use

-- ========================================
-- 6. SUCCESS MESSAGE
-- ========================================
SELECT '✅ CLAIMS & CLAIM_HISTORY SCHEMA FIX COMPLETED!' as status;
SELECT 'All missing columns added with nullable constraints.' as message;
SELECT 'Ready for real-time email processing and Zendesk integration.' as note;
