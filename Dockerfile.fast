# 🚀 Fast Dockerfile for Development
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app/src \
    PIP_NO_CACHE_DIR=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create working directory
WORKDIR /app

# Copy only requirements first (better caching)
COPY requirements.txt .

# Install only production dependencies
RUN pip install --no-cache-dir \
    fastapi>=0.100.0 \
    uvicorn>=0.23.0 \
    openai>=1.30.0 \
    pydantic>=2.0.0 \
    pydantic-settings>=2.0.0 \
    requests>=2.31.0 \
    python-dotenv>=1.0.0 \
    supabase>=2.3.0 \
    baml-py>=0.45.0 \
    python-multipart>=0.0.6 \
    aiofiles>=23.2.0

# Copy application code
COPY . .

# Generate BAML client
RUN baml-cli generate

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
