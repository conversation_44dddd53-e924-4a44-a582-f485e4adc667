-- 🚀 Optimized Supabase Schema for High Performance
-- Run this to add optimizations to existing tables

-- 1. Add missing indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_claims_created_at ON claims(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_claims_email_type ON claims(email_type);
CREATE INDEX IF NOT EXISTS idx_claims_claim_type ON claims(claim_type);
CREATE INDEX IF NOT EXISTS idx_claims_urgency_level ON claims(urgency_level);
CREATE INDEX IF NOT EXISTS idx_claims_confidence ON claims(confidence DESC);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_claims_status_created ON claims(status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_claims_type_status ON claims(email_type, status);
CREATE INDEX IF NOT EXISTS idx_claims_sender_created ON claims(sender_email, created_at DESC);

-- Zendesk tickets optimizations
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_status ON zendesk_tickets(status);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_priority ON zendesk_tickets(priority);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_sync_status ON zendesk_tickets(sync_status);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_created_at ON zendesk_tickets(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_last_sync ON zendesk_tickets(last_sync_at DESC);

-- Attachments optimizations
CREATE INDEX IF NOT EXISTS idx_attachments_filename ON attachments(original_filename);
CREATE INDEX IF NOT EXISTS idx_attachments_content_type ON attachments(content_type);
CREATE INDEX IF NOT EXISTS idx_attachments_upload_status ON attachments(upload_status);
CREATE INDEX IF NOT EXISTS idx_attachments_created_at ON attachments(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_attachments_file_size ON attachments(file_size);

-- Claim history optimizations
CREATE INDEX IF NOT EXISTS idx_claim_history_event_type ON claim_history(event_type);
CREATE INDEX IF NOT EXISTS idx_claim_history_created_at ON claim_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_claim_history_claim_event ON claim_history(claim_id, event_type, created_at DESC);

-- 2. Add constraints for data integrity
ALTER TABLE claims 
ADD CONSTRAINT IF NOT EXISTS chk_confidence_range 
CHECK (confidence >= 0.0 AND confidence <= 1.0);

ALTER TABLE claims 
ADD CONSTRAINT IF NOT EXISTS chk_email_type_valid 
CHECK (email_type IN ('CLAIM_SUBMISSION', 'INQUIRY', 'COMPLAINT', 'OTHER'));

ALTER TABLE claims 
ADD CONSTRAINT IF NOT EXISTS chk_claim_type_valid 
CHECK (claim_type IN ('AUTO', 'HOME', 'LIFE', 'HEALTH', 'BUSINESS', 'OTHER'));

ALTER TABLE claims 
ADD CONSTRAINT IF NOT EXISTS chk_urgency_level_valid 
CHECK (urgency_level IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL'));

ALTER TABLE claims 
ADD CONSTRAINT IF NOT EXISTS chk_status_valid 
CHECK (status IN ('PENDING', 'PROCESSING', 'PROCESSED', 'COMPLETED', 'REJECTED', 'SETUP_TEST', 'POLICY_TEST'));

-- Zendesk tickets constraints
ALTER TABLE zendesk_tickets 
ADD CONSTRAINT IF NOT EXISTS chk_zendesk_status_valid 
CHECK (status IN ('new', 'open', 'pending', 'hold', 'solved', 'closed'));

ALTER TABLE zendesk_tickets 
ADD CONSTRAINT IF NOT EXISTS chk_zendesk_priority_valid 
CHECK (priority IN ('low', 'normal', 'high', 'urgent'));

ALTER TABLE zendesk_tickets 
ADD CONSTRAINT IF NOT EXISTS chk_sync_status_valid 
CHECK (sync_status IN ('pending', 'synced', 'failed', 'retry'));

-- Attachments constraints
ALTER TABLE attachments 
ADD CONSTRAINT IF NOT EXISTS chk_file_size_positive 
CHECK (file_size > 0);

ALTER TABLE attachments 
ADD CONSTRAINT IF NOT EXISTS chk_upload_status_valid 
CHECK (upload_status IN ('uploading', 'uploaded', 'failed', 'deleted'));

-- 3. Add useful functions for common operations
CREATE OR REPLACE FUNCTION get_claim_summary(claim_uuid UUID)
RETURNS TABLE(
    claim_id UUID,
    workflow_id TEXT,
    subject TEXT,
    status TEXT,
    zendesk_ticket_id TEXT,
    attachment_count BIGINT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.workflow_id,
        c.subject,
        c.status,
        zt.zendesk_ticket_id,
        COALESCE(att_count.count, 0) as attachment_count,
        c.created_at
    FROM claims c
    LEFT JOIN zendesk_tickets zt ON c.id = zt.claim_id
    LEFT JOIN (
        SELECT claim_id, COUNT(*) as count 
        FROM attachments 
        WHERE upload_status = 'uploaded'
        GROUP BY claim_id
    ) att_count ON c.id = att_count.claim_id
    WHERE c.id = claim_uuid;
END;
$$ LANGUAGE plpgsql;

-- 4. Add triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to tables with updated_at columns
DROP TRIGGER IF EXISTS update_claims_updated_at ON claims;
CREATE TRIGGER update_claims_updated_at
    BEFORE UPDATE ON claims
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_zendesk_tickets_updated_at ON zendesk_tickets;
CREATE TRIGGER update_zendesk_tickets_updated_at
    BEFORE UPDATE ON zendesk_tickets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 5. Create views for common queries
CREATE OR REPLACE VIEW active_claims AS
SELECT 
    c.*,
    zt.zendesk_ticket_id,
    zt.zendesk_url,
    COUNT(a.id) as attachment_count
FROM claims c
LEFT JOIN zendesk_tickets zt ON c.id = zt.claim_id
LEFT JOIN attachments a ON c.id = a.claim_id AND a.upload_status = 'uploaded'
WHERE c.status IN ('PENDING', 'PROCESSING')
GROUP BY c.id, zt.zendesk_ticket_id, zt.zendesk_url
ORDER BY c.created_at DESC;

CREATE OR REPLACE VIEW recent_claims AS
SELECT 
    c.*,
    zt.zendesk_ticket_id,
    COUNT(a.id) as attachment_count
FROM claims c
LEFT JOIN zendesk_tickets zt ON c.id = zt.claim_id
LEFT JOIN attachments a ON c.id = a.claim_id AND a.upload_status = 'uploaded'
WHERE c.created_at >= NOW() - INTERVAL '7 days'
GROUP BY c.id, zt.zendesk_ticket_id
ORDER BY c.created_at DESC;

-- 6. Performance monitoring query
CREATE OR REPLACE VIEW table_stats AS
SELECT 
    'claims' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as last_24h,
    COUNT(*) FILTER (WHERE status = 'PENDING') as pending_count
FROM claims
UNION ALL
SELECT 
    'zendesk_tickets' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as last_24h,
    COUNT(*) FILTER (WHERE sync_status = 'pending') as pending_count
FROM zendesk_tickets
UNION ALL
SELECT 
    'attachments' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as last_24h,
    COUNT(*) FILTER (WHERE upload_status = 'uploaded') as uploaded_count
FROM attachments;

-- Test the optimizations
SELECT 'Schema optimization completed!' as status;
SELECT * FROM table_stats;
