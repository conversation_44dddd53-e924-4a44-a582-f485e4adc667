-- 🔧 Fix Missing Columns in Supabase Database
-- Run this script in your Supabase SQL Editor to add missing columns
-- ALL COLUMNS ARE NULLABLE TO PREVENT INSERTION ERRORS

-- Add missing columns to claims table (ALL NULLABLE)
ALTER TABLE claims
ADD COLUMN IF NOT EXISTS ai_classification_result JSONB NULL,
ADD COLUMN IF NOT EXISTS consensus_confidence DECIMAL(5,4) NULL,
ADD COLUMN IF NOT EXISTS policy_number TEXT NULL,
ADD COLUMN IF NOT EXISTS incident_date TIMESTAMP WITH TIME ZONE NULL,
ADD COLUMN IF NOT EXISTS incident_location TEXT NULL,
ADD COLUMN IF NOT EXISTS estimated_value DECIMAL(12,2) NULL,
ADD COLUMN IF NOT EXISTS claimant_name TEXT NULL,
ADD COLUMN IF NOT EXISTS claimant_phone TEXT NULL,
ADD COLUMN IF NOT EXISTS email_subject TEXT NULL,
ADD COLUMN IF NOT EXISTS email_body TEXT NULL,
ADD COLUMN IF NOT EXISTS sender_name TEXT NULL,
ADD COLUMN IF NOT EXISTS received_at TIMESTAMP WITH TIME ZONE NULL,
ADD COLUMN IF NOT EXISTS confidence_level TEXT NULL,
ADD COLUMN IF NOT EXISTS priority_score INTEGER NULL,
ADD COLUMN IF NOT EXISTS zendesk_status TEXT NULL,
ADD COLUMN IF NOT EXISTS attachments_count INTEGER NULL,
ADD COLUMN IF NOT EXISTS zendesk_ticket_id TEXT NULL,
ADD COLUMN IF NOT EXISTS zendesk_ticket_url TEXT NULL;

-- Add missing columns to zendesk_tickets table (ALL NULLABLE)
ALTER TABLE zendesk_tickets
ADD COLUMN IF NOT EXISTS description TEXT NULL,
ADD COLUMN IF NOT EXISTS assignee_id TEXT NULL,
ADD COLUMN IF NOT EXISTS assignee_email TEXT NULL,
ADD COLUMN IF NOT EXISTS group_id TEXT NULL,
ADD COLUMN IF NOT EXISTS ai_priority_score INTEGER NULL,
ADD COLUMN IF NOT EXISTS complexity_level TEXT NULL,
ADD COLUMN IF NOT EXISTS estimated_resolution_hours INTEGER NULL,
ADD COLUMN IF NOT EXISTS sync_error TEXT NULL;

-- Add missing columns to attachments table (ALL NULLABLE)
ALTER TABLE attachments
ADD COLUMN IF NOT EXISTS attachment_id UUID NULL,
ADD COLUMN IF NOT EXISTS filename TEXT NULL,
ADD COLUMN IF NOT EXISTS storage_bucket TEXT NULL,
ADD COLUMN IF NOT EXISTS storage_url TEXT NULL,
ADD COLUMN IF NOT EXISTS status TEXT NULL,
ADD COLUMN IF NOT EXISTS ocr_text TEXT NULL,
ADD COLUMN IF NOT EXISTS ocr_confidence DECIMAL(5,4) NULL,
ADD COLUMN IF NOT EXISTS document_type TEXT NULL,
ADD COLUMN IF NOT EXISTS upload_metadata JSONB NULL,
ADD COLUMN IF NOT EXISTS processing_metadata JSONB NULL,
ADD COLUMN IF NOT EXISTS uploaded_at TIMESTAMP WITH TIME ZONE NULL,
ADD COLUMN IF NOT EXISTS processed_at TIMESTAMP WITH TIME ZONE NULL;

-- Add missing columns to claim_history table (ALL NULLABLE)
ALTER TABLE claim_history
ADD COLUMN IF NOT EXISTS event_description TEXT NULL,
ADD COLUMN IF NOT EXISTS triggered_by TEXT NULL,
ADD COLUMN IF NOT EXISTS user_id TEXT NULL,
ADD COLUMN IF NOT EXISTS processing_step TEXT NULL,
ADD COLUMN IF NOT EXISTS context_metadata JSONB NULL,
ADD COLUMN IF NOT EXISTS error_details JSONB NULL;

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_claims_policy_number ON claims(policy_number);
CREATE INDEX IF NOT EXISTS idx_claims_incident_date ON claims(incident_date);
CREATE INDEX IF NOT EXISTS idx_claims_zendesk_ticket_id ON claims(zendesk_ticket_id);
CREATE INDEX IF NOT EXISTS idx_claims_claimant_name ON claims(claimant_name);
CREATE INDEX IF NOT EXISTS idx_claims_sender_email ON claims(sender_email);

-- Indexes for zendesk_tickets
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_assignee_email ON zendesk_tickets(assignee_email);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_group_id ON zendesk_tickets(group_id);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_ai_priority_score ON zendesk_tickets(ai_priority_score);

-- Indexes for attachments
CREATE INDEX IF NOT EXISTS idx_attachments_attachment_id ON attachments(attachment_id);
CREATE INDEX IF NOT EXISTS idx_attachments_filename ON attachments(filename);
CREATE INDEX IF NOT EXISTS idx_attachments_status ON attachments(status);
CREATE INDEX IF NOT EXISTS idx_attachments_document_type ON attachments(document_type);

-- Indexes for claim_history
CREATE INDEX IF NOT EXISTS idx_claim_history_event_type ON claim_history(event_type);
CREATE INDEX IF NOT EXISTS idx_claim_history_triggered_by ON claim_history(triggered_by);
CREATE INDEX IF NOT EXISTS idx_claim_history_processing_step ON claim_history(processing_step);

-- No default value updates needed - all columns are nullable

-- Verify the schema changes
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'claims' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- Verify all table schemas
SELECT 'claims' as table_name, column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'claims' AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT 'zendesk_tickets' as table_name, column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'zendesk_tickets' AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT 'attachments' as table_name, column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'attachments' AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT 'claim_history' as table_name, column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'claim_history' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Show updated table counts
SELECT
    'claims' as table_name,
    count(*) as record_count
FROM claims
UNION ALL
SELECT
    'zendesk_tickets' as table_name,
    count(*) as record_count
FROM zendesk_tickets
UNION ALL
SELECT
    'attachments' as table_name,
    count(*) as record_count
FROM attachments
UNION ALL
SELECT
    'claim_history' as table_name,
    count(*) as record_count
FROM claim_history;
