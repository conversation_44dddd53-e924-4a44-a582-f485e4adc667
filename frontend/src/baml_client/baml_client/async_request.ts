/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: do not edit it. Instead, edit the BAML
// files and re-generate this code.
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BamlCtxManager, ClientRegistry, Image, Audio } from "@boundaryml/baml"
import { toBamlError, HTTPRequest } from "@boundaryml/baml"
import type { Checked, Check } from "./types"
import type * as types from "./types"
import type {ClaimDecision, ClaimRecommendation, ClaimType, ConfidenceLevel, CoverageStatus, DocumentAnalysis, DocumentType, EmailAnalysis, EmailType, FinancialAssessment, LegalAnalysis, LegalRisk, LiabilityAssessment, ModelConsensus, NextAction, PolicyVerification, UrgencyLevel} from "./types"
import type TypeBuilder from "./type_builder"

type BamlCallOptions = {
  tb?: TypeBuilder
  clientRegistry?: ClientRegistry
  env?: Record<string, string | undefined>
}

export class AsyncHttpRequest {
  constructor(private runtime: BamlRuntime, private ctxManager: BamlCtxManager) {}

  
  async AnalyzeDocument(
      document_text: string,document_filename: string,claim_context: string,
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "AnalyzeDocument",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_context": claim_context
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        false,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async AssessLegalRisk(
      incident_description: string,location: string,parties_involved: string,claim_value: number,
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "AssessLegalRisk",
        {
          "incident_description": incident_description,"location": location,"parties_involved": parties_involved,"claim_value": claim_value
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        false,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async ClassifyEmail(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "ClassifyEmail",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        false,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async ValidateEmailClassification(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "ValidateEmailClassification",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        false,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async VerifyPolicyDetails(
      policy_number: string,incident_date: string,claim_type: string,policy_data: string,
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "VerifyPolicyDetails",
        {
          "policy_number": policy_number,"incident_date": incident_date,"claim_type": claim_type,"policy_data": policy_data
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        false,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}

export class AsyncHttpStreamRequest {
  constructor(private runtime: BamlRuntime, private ctxManager: BamlCtxManager) {}

  
  async AnalyzeDocument(
      document_text: string,document_filename: string,claim_context: string,
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "AnalyzeDocument",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_context": claim_context
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        true,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async AssessLegalRisk(
      incident_description: string,location: string,parties_involved: string,claim_value: number,
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "AssessLegalRisk",
        {
          "incident_description": incident_description,"location": location,"parties_involved": parties_involved,"claim_value": claim_value
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        true,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async ClassifyEmail(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "ClassifyEmail",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        true,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async ValidateEmailClassification(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "ValidateEmailClassification",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        true,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async VerifyPolicyDetails(
      policy_number: string,incident_date: string,claim_type: string,policy_data: string,
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return await this.runtime.buildRequest(
        "VerifyPolicyDetails",
        {
          "policy_number": policy_number,"incident_date": incident_date,"claim_type": claim_type,"policy_data": policy_data
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        true,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}