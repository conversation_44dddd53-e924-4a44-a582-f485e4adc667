/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: do not edit it. Instead, edit the BAML
// files and re-generate this code.
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
import { FieldType } from '@boundaryml/baml/native'
import { TypeBuilder as _TypeBuilder, EnumBuilder, EnumViewer, ClassBuilder, ClassViewer } from '@boundaryml/baml/type_builder'
import { DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME } from "./globals"

export default class TypeBuilder {
    private tb: _TypeBuilder;
    
    ClaimRecommendation: ClassViewer<'ClaimRecommendation', "decision" | "next_actions" | "priority_level" | "target_response_time" | "requires_human_approval" | "approval_reason" | "recommended_approver" | "customer_communication_required" | "communication_template" | "zendesk_priority" | "zendesk_tags" | "recommendation_summary">;
    
    DocumentAnalysis: ClassViewer<'DocumentAnalysis', "document_type" | "confidence" | "key_information" | "dates_mentioned" | "amounts_mentioned" | "is_readable" | "quality_score" | "is_relevant_to_claim" | "relevance_explanation">;
    
    EmailAnalysis: ClassViewer<'EmailAnalysis', "email_type" | "is_claim" | "claim_type" | "urgency_level" | "confidence" | "policy_number" | "claim_number" | "incident_date" | "location" | "customer_name" | "customer_phone" | "customer_email" | "summary" | "key_details" | "attachments_mentioned" | "requires_human_review" | "requires_immediate_action" | "reasoning">;
    
    FinancialAssessment: ClassViewer<'FinancialAssessment', "estimated_claim_value" | "confidence_range_min" | "confidence_range_max" | "property_damage_estimate" | "medical_costs_estimate" | "legal_costs_estimate" | "other_costs_estimate" | "exceeds_authority_limit" | "requires_reserves" | "financial_notes">;
    
    LegalAnalysis: ClassViewer<'LegalAnalysis', "legal_risk" | "liability_assessment" | "applicable_province" | "relevant_statutes" | "precedent_cases" | "risk_factors" | "mitigation_strategies" | "legal_notes">;
    
    ModelConsensus: ClassViewer<'ModelConsensus', "claude_analysis" | "gpt4_analysis" | "consensus_reached" | "consensus_confidence" | "disagreement_areas" | "tie_breaker_needed" | "final_analysis" | "consensus_notes">;
    
    PolicyVerification: ClassViewer<'PolicyVerification', "policy_found" | "policy_active" | "coverage_status" | "policy_type" | "coverage_limits" | "deductible" | "verification_notes" | "exclusions_apply" | "exclusion_details">;
    
    
    ClaimDecision: EnumViewer<'ClaimDecision', "APPROVE_PAYMENT" | "DENY_CLAIM" | "REQUIRES_INVESTIGATION" | "REQUIRES_HUMAN_APPROVAL" | "REQUEST_MORE_INFORMATION">;
    
    ClaimType: EnumViewer<'ClaimType', "AUTO_ACCIDENT" | "PROPERTY_DAMAGE" | "LIABILITY" | "PERSONAL_INJURY" | "THEFT" | "FIRE_DAMAGE" | "WATER_DAMAGE" | "VANDALISM" | "OTHER">;
    
    ConfidenceLevel: EnumViewer<'ConfidenceLevel', "VERY_LOW" | "LOW" | "MEDIUM" | "HIGH" | "VERY_HIGH">;
    
    CoverageStatus: EnumViewer<'CoverageStatus', "COVERED" | "NOT_COVERED" | "PARTIALLY_COVERED" | "REQUIRES_INVESTIGATION" | "POLICY_NOT_FOUND">;
    
    DocumentType: EnumViewer<'DocumentType', "POLICE_REPORT" | "MEDICAL_REPORT" | "REPAIR_ESTIMATE" | "PHOTO_EVIDENCE" | "INSURANCE_FORM" | "RECEIPT" | "INVOICE" | "WITNESS_STATEMENT" | "CORRESPONDENCE" | "OTHER_DOCUMENT">;
    
    EmailType: EnumViewer<'EmailType', "CLAIM_SUBMISSION" | "CLAIM_INQUIRY" | "POLICY_QUESTION" | "BILLING_INQUIRY" | "GENERAL_INQUIRY" | "SPAM" | "NOT_INSURANCE_RELATED">;
    
    LegalRisk: EnumViewer<'LegalRisk', "VERY_LOW" | "LOW" | "MEDIUM" | "HIGH" | "VERY_HIGH">;
    
    LiabilityAssessment: EnumViewer<'LiabilityAssessment', "CLEAR_LIABILITY" | "DISPUTED_LIABILITY" | "NO_LIABILITY" | "REQUIRES_INVESTIGATION">;
    
    NextAction: EnumViewer<'NextAction', "CREATE_ZENDESK_TICKET" | "SCHEDULE_INSPECTION" | "REQUEST_DOCUMENTS" | "CONTACT_CUSTOMER" | "ESCALATE_TO_MANAGER" | "CLOSE_INQUIRY" | "TRANSFER_TO_SPECIALIST">;
    
    UrgencyLevel: EnumViewer<'UrgencyLevel', "LOW" | "MEDIUM" | "HIGH" | "CRITICAL">;
    

    constructor() {
        this.tb = new _TypeBuilder({
          classes: new Set([
            "ClaimRecommendation","DocumentAnalysis","EmailAnalysis","FinancialAssessment","LegalAnalysis","ModelConsensus","PolicyVerification",
          ]),
          enums: new Set([
            "ClaimDecision","ClaimType","ConfidenceLevel","CoverageStatus","DocumentType","EmailType","LegalRisk","LiabilityAssessment","NextAction","UrgencyLevel",
          ]),
          runtime: DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME
        });
        
        this.ClaimRecommendation = this.tb.classViewer("ClaimRecommendation", [
          "decision","next_actions","priority_level","target_response_time","requires_human_approval","approval_reason","recommended_approver","customer_communication_required","communication_template","zendesk_priority","zendesk_tags","recommendation_summary",
        ]);
        
        this.DocumentAnalysis = this.tb.classViewer("DocumentAnalysis", [
          "document_type","confidence","key_information","dates_mentioned","amounts_mentioned","is_readable","quality_score","is_relevant_to_claim","relevance_explanation",
        ]);
        
        this.EmailAnalysis = this.tb.classViewer("EmailAnalysis", [
          "email_type","is_claim","claim_type","urgency_level","confidence","policy_number","claim_number","incident_date","location","customer_name","customer_phone","customer_email","summary","key_details","attachments_mentioned","requires_human_review","requires_immediate_action","reasoning",
        ]);
        
        this.FinancialAssessment = this.tb.classViewer("FinancialAssessment", [
          "estimated_claim_value","confidence_range_min","confidence_range_max","property_damage_estimate","medical_costs_estimate","legal_costs_estimate","other_costs_estimate","exceeds_authority_limit","requires_reserves","financial_notes",
        ]);
        
        this.LegalAnalysis = this.tb.classViewer("LegalAnalysis", [
          "legal_risk","liability_assessment","applicable_province","relevant_statutes","precedent_cases","risk_factors","mitigation_strategies","legal_notes",
        ]);
        
        this.ModelConsensus = this.tb.classViewer("ModelConsensus", [
          "claude_analysis","gpt4_analysis","consensus_reached","consensus_confidence","disagreement_areas","tie_breaker_needed","final_analysis","consensus_notes",
        ]);
        
        this.PolicyVerification = this.tb.classViewer("PolicyVerification", [
          "policy_found","policy_active","coverage_status","policy_type","coverage_limits","deductible","verification_notes","exclusions_apply","exclusion_details",
        ]);
        
        
        this.ClaimDecision = this.tb.enumViewer("ClaimDecision", [
          "APPROVE_PAYMENT","DENY_CLAIM","REQUIRES_INVESTIGATION","REQUIRES_HUMAN_APPROVAL","REQUEST_MORE_INFORMATION",
        ]);
        
        this.ClaimType = this.tb.enumViewer("ClaimType", [
          "AUTO_ACCIDENT","PROPERTY_DAMAGE","LIABILITY","PERSONAL_INJURY","THEFT","FIRE_DAMAGE","WATER_DAMAGE","VANDALISM","OTHER",
        ]);
        
        this.ConfidenceLevel = this.tb.enumViewer("ConfidenceLevel", [
          "VERY_LOW","LOW","MEDIUM","HIGH","VERY_HIGH",
        ]);
        
        this.CoverageStatus = this.tb.enumViewer("CoverageStatus", [
          "COVERED","NOT_COVERED","PARTIALLY_COVERED","REQUIRES_INVESTIGATION","POLICY_NOT_FOUND",
        ]);
        
        this.DocumentType = this.tb.enumViewer("DocumentType", [
          "POLICE_REPORT","MEDICAL_REPORT","REPAIR_ESTIMATE","PHOTO_EVIDENCE","INSURANCE_FORM","RECEIPT","INVOICE","WITNESS_STATEMENT","CORRESPONDENCE","OTHER_DOCUMENT",
        ]);
        
        this.EmailType = this.tb.enumViewer("EmailType", [
          "CLAIM_SUBMISSION","CLAIM_INQUIRY","POLICY_QUESTION","BILLING_INQUIRY","GENERAL_INQUIRY","SPAM","NOT_INSURANCE_RELATED",
        ]);
        
        this.LegalRisk = this.tb.enumViewer("LegalRisk", [
          "VERY_LOW","LOW","MEDIUM","HIGH","VERY_HIGH",
        ]);
        
        this.LiabilityAssessment = this.tb.enumViewer("LiabilityAssessment", [
          "CLEAR_LIABILITY","DISPUTED_LIABILITY","NO_LIABILITY","REQUIRES_INVESTIGATION",
        ]);
        
        this.NextAction = this.tb.enumViewer("NextAction", [
          "CREATE_ZENDESK_TICKET","SCHEDULE_INSPECTION","REQUEST_DOCUMENTS","CONTACT_CUSTOMER","ESCALATE_TO_MANAGER","CLOSE_INQUIRY","TRANSFER_TO_SPECIALIST",
        ]);
        
        this.UrgencyLevel = this.tb.enumViewer("UrgencyLevel", [
          "LOW","MEDIUM","HIGH","CRITICAL",
        ]);
        
    }

    __tb() {
      return this.tb._tb();
    }

    string(): FieldType {
        return this.tb.string()
    }

    literalString(value: string): FieldType {
        return this.tb.literalString(value)
    }

    literalInt(value: number): FieldType {
        return this.tb.literalInt(value)
    }

    literalBool(value: boolean): FieldType {
        return this.tb.literalBool(value)
    }

    int(): FieldType {
        return this.tb.int()
    }

    float(): FieldType {
        return this.tb.float()
    }

    bool(): FieldType {
        return this.tb.bool()
    }

    list(type: FieldType): FieldType {
        return this.tb.list(type)
    }

    null(): FieldType {
        return this.tb.null()
    }

    map(key: FieldType, value: FieldType): FieldType {
        return this.tb.map(key, value)
    }

    union(types: FieldType[]): FieldType {
        return this.tb.union(types)
    }

    addClass<Name extends string>(name: Name): ClassBuilder<Name> {
        return this.tb.addClass(name);
    }

    addEnum<Name extends string>(name: Name): EnumBuilder<Name> {
        return this.tb.addEnum(name);
    }

    addBaml(baml: string): void {
        this.tb.addBaml(baml);
    }
}