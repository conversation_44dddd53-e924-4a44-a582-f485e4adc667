/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: do not edit it. Instead, edit the BAML
// files and re-generate this code.
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
import type { <PERSON><PERSON>l<PERSON>unt<PERSON>, FunctionResult, BamlCtxManager, ClientRegistry, Image, Audio, Collector } from "@boundaryml/baml"
import { toBamlError, BamlStream, type HTTPRequest } from "@boundaryml/baml"
import type { Checked, Check, RecursivePartialNull as MovedRecursivePartialNull } from "./types"
import type { partial_types } from "./partial_types"
import type * as types from "./types"
import type {ClaimDecision, ClaimRecommendation, ClaimType, ConfidenceLevel, CoverageStatus, DocumentAnalysis, DocumentType, EmailAnalysis, EmailType, FinancialAssessment, LegalAnalysis, LegalRisk, LiabilityAssessment, ModelConsensus, NextAction, PolicyVerification, UrgencyLevel} from "./types"
import type TypeBuilder from "./type_builder"
import { AsyncHttpRequest, AsyncHttpStreamRequest } from "./async_request"
import { LlmResponseParser, LlmStreamParser } from "./parser"
import { DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME } from "./globals"

/**
 * @deprecated Use RecursivePartialNull from 'baml_client/types' instead.
 */
export type RecursivePartialNull<T> = MovedRecursivePartialNull<T>

type BamlCallOptions = {
  tb?: TypeBuilder
  clientRegistry?: ClientRegistry
  collector?: Collector | Collector[]
  env?: Record<string, string | undefined>
}

export class BamlAsyncClient {
  private runtime: BamlRuntime
  private ctxManager: BamlCtxManager
  private streamClient: BamlStreamClient
  private httpRequest: AsyncHttpRequest
  private httpStreamRequest: AsyncHttpStreamRequest
  private llmResponseParser: LlmResponseParser
  private llmStreamParser: LlmStreamParser
  private bamlOptions: BamlCallOptions

  constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager, bamlOptions?: BamlCallOptions) {
    this.runtime = runtime
    this.ctxManager = ctxManager
    this.streamClient = new BamlStreamClient(runtime, ctxManager, bamlOptions)
    this.httpRequest = new AsyncHttpRequest(runtime, ctxManager)
    this.httpStreamRequest = new AsyncHttpStreamRequest(runtime, ctxManager)
    this.llmResponseParser = new LlmResponseParser(runtime, ctxManager)
    this.llmStreamParser = new LlmStreamParser(runtime, ctxManager)
    this.bamlOptions = bamlOptions || {}
  }

  withOptions(bamlOptions: BamlCallOptions) {
    return new BamlAsyncClient(this.runtime, this.ctxManager, bamlOptions)
  }

  get stream() {
    return this.streamClient
  }

  get request() {
    return this.httpRequest
  }

  get streamRequest() {
    return this.httpStreamRequest
  }

  get parse() {
    return this.llmResponseParser
  }

  get parseStream() {
    return this.llmStreamParser
  }

  
  async AnalyzeDocument(
      document_text: string,document_filename: string,claim_context: string,
      __baml_options__?: BamlCallOptions
  ): Promise<DocumentAnalysis> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = await this.runtime.callFunction(
        "AnalyzeDocument",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_context": claim_context
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as DocumentAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async AssessLegalRisk(
      incident_description: string,location: string,parties_involved: string,claim_value: number,
      __baml_options__?: BamlCallOptions
  ): Promise<LegalAnalysis> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = await this.runtime.callFunction(
        "AssessLegalRisk",
        {
          "incident_description": incident_description,"location": location,"parties_involved": parties_involved,"claim_value": claim_value
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as LegalAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async ClassifyEmail(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: BamlCallOptions
  ): Promise<EmailAnalysis> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = await this.runtime.callFunction(
        "ClassifyEmail",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as EmailAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async ValidateEmailClassification(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: BamlCallOptions
  ): Promise<EmailAnalysis> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = await this.runtime.callFunction(
        "ValidateEmailClassification",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as EmailAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  async VerifyPolicyDetails(
      policy_number: string,incident_date: string,claim_type: string,policy_data: string,
      __baml_options__?: BamlCallOptions
  ): Promise<PolicyVerification> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = await this.runtime.callFunction(
        "VerifyPolicyDetails",
        {
          "policy_number": policy_number,"incident_date": incident_date,"claim_type": claim_type,"policy_data": policy_data
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as PolicyVerification
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}

class BamlStreamClient {
  private runtime: BamlRuntime
  private ctxManager: BamlCtxManager
  private bamlOptions: BamlCallOptions

  constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager, bamlOptions?: BamlCallOptions) {
    this.runtime = runtime
    this.ctxManager = ctxManager
    this.bamlOptions = bamlOptions || { env: { ...process.env } }
  }

  
  AnalyzeDocument(
      document_text: string,document_filename: string,claim_context: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry, collector?: Collector | Collector[], env?: Record<string, string | undefined> }
  ): BamlStream<partial_types.DocumentAnalysis, DocumentAnalysis> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.streamFunction(
        "AnalyzeDocument",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_context": claim_context
        },
        undefined,
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return new BamlStream<partial_types.DocumentAnalysis, DocumentAnalysis>(
        raw,
        (a): partial_types.DocumentAnalysis => a,
        (a): DocumentAnalysis => a,
        this.ctxManager.cloneContext(),
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  AssessLegalRisk(
      incident_description: string,location: string,parties_involved: string,claim_value: number,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry, collector?: Collector | Collector[], env?: Record<string, string | undefined> }
  ): BamlStream<partial_types.LegalAnalysis, LegalAnalysis> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.streamFunction(
        "AssessLegalRisk",
        {
          "incident_description": incident_description,"location": location,"parties_involved": parties_involved,"claim_value": claim_value
        },
        undefined,
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return new BamlStream<partial_types.LegalAnalysis, LegalAnalysis>(
        raw,
        (a): partial_types.LegalAnalysis => a,
        (a): LegalAnalysis => a,
        this.ctxManager.cloneContext(),
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  ClassifyEmail(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry, collector?: Collector | Collector[], env?: Record<string, string | undefined> }
  ): BamlStream<partial_types.EmailAnalysis, EmailAnalysis> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.streamFunction(
        "ClassifyEmail",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        undefined,
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return new BamlStream<partial_types.EmailAnalysis, EmailAnalysis>(
        raw,
        (a): partial_types.EmailAnalysis => a,
        (a): EmailAnalysis => a,
        this.ctxManager.cloneContext(),
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  ValidateEmailClassification(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry, collector?: Collector | Collector[], env?: Record<string, string | undefined> }
  ): BamlStream<partial_types.EmailAnalysis, EmailAnalysis> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.streamFunction(
        "ValidateEmailClassification",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        undefined,
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return new BamlStream<partial_types.EmailAnalysis, EmailAnalysis>(
        raw,
        (a): partial_types.EmailAnalysis => a,
        (a): EmailAnalysis => a,
        this.ctxManager.cloneContext(),
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  VerifyPolicyDetails(
      policy_number: string,incident_date: string,claim_type: string,policy_data: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry, collector?: Collector | Collector[], env?: Record<string, string | undefined> }
  ): BamlStream<partial_types.PolicyVerification, PolicyVerification> {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.streamFunction(
        "VerifyPolicyDetails",
        {
          "policy_number": policy_number,"incident_date": incident_date,"claim_type": claim_type,"policy_data": policy_data
        },
        undefined,
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return new BamlStream<partial_types.PolicyVerification, PolicyVerification>(
        raw,
        (a): partial_types.PolicyVerification => a,
        (a): PolicyVerification => a,
        this.ctxManager.cloneContext(),
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}

export const b = new BamlAsyncClient(DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX)