/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: do not edit it. Instead, edit the BAML
// files and re-generate this code.
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
import type { <PERSON><PERSON><PERSON><PERSON>untime, BamlCtxManager, ClientRegistry, Image, Audio, Collector } from "@boundaryml/baml"
import { toBamlError } from "@boundaryml/baml"
import type { Checked, Check } from "./types"
import type { partial_types } from "./partial_types"
import type * as types from "./types"
import type {ClaimDecision, ClaimRecommendation, ClaimType, ConfidenceLevel, CoverageStatus, DocumentAnalysis, DocumentType, EmailAnalysis, EmailType, FinancialAssessment, LegalAnalysis, LegalRisk, LiabilityAssessment, ModelConsensus, NextAction, PolicyVerification, UrgencyLevel} from "./types"
import type TypeBuilder from "./type_builder"

export class LlmResponseParser {
  constructor(private runtime: BamlRuntime, private ctxManager: BamlCtxManager) {}

  
  AnalyzeDocument(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): DocumentAnalysis {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "AnalyzeDocument",
        llmResponse,
        false,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as DocumentAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  AssessLegalRisk(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): LegalAnalysis {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "AssessLegalRisk",
        llmResponse,
        false,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as LegalAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  ClassifyEmail(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): EmailAnalysis {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "ClassifyEmail",
        llmResponse,
        false,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as EmailAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  ValidateEmailClassification(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): EmailAnalysis {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "ValidateEmailClassification",
        llmResponse,
        false,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as EmailAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  VerifyPolicyDetails(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): PolicyVerification {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "VerifyPolicyDetails",
        llmResponse,
        false,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as PolicyVerification
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}

export class LlmStreamParser {
  constructor(private runtime: BamlRuntime, private ctxManager: BamlCtxManager) {}

  
  AnalyzeDocument(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): partial_types.DocumentAnalysis {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "AnalyzeDocument",
        llmResponse,
        true,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as partial_types.DocumentAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  AssessLegalRisk(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): partial_types.LegalAnalysis {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "AssessLegalRisk",
        llmResponse,
        true,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as partial_types.LegalAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  ClassifyEmail(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): partial_types.EmailAnalysis {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "ClassifyEmail",
        llmResponse,
        true,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as partial_types.EmailAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  ValidateEmailClassification(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): partial_types.EmailAnalysis {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "ValidateEmailClassification",
        llmResponse,
        true,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as partial_types.EmailAnalysis
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
  VerifyPolicyDetails(
      llmResponse: string,
      __baml_options__?: { tb?: TypeBuilder, clientRegistry?: ClientRegistry }
  ): partial_types.PolicyVerification {
    try {
      const env = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      return this.runtime.parseLlmResponse(
        "VerifyPolicyDetails",
        llmResponse,
        true,
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        env,
      ) as partial_types.PolicyVerification
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}