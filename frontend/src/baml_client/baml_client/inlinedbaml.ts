/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: do not edit it. Instead, edit the BAML
// files and re-generate this code.
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
const fileMap = {
  
  "clients.baml": "// 🔧 BAML Client Configuration for Zurich AI Claims Processing\n// Defines LLM clients for multi-model consensus engine\n\n// =============================================================================\n// 🔄 RETRY POLICIES\n// =============================================================================\n\nretry_policy StandardRetry {\n  max_retries 3\n  strategy {\n    type exponential_backoff\n    delay_ms 200\n    multiplier 1.5\n    max_delay_ms 10000\n  }\n}\n\nretry_policy FastRetry {\n  max_retries 2\n  strategy {\n    type constant_delay\n    delay_ms 100\n  }\n}\n\n// =============================================================================\n// 🤖 PRIMARY AI MODELS\n// =============================================================================\n\n// GPT-4o - Primary reasoning model\nclient<llm> GPT4o {\n  provider \"openai\"\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n    max_tokens 4000\n    temperature 0.1  // Low temperature for consistent analysis\n  }\n}\n\n// Claude 3.5 Sonnet - Backup model (if needed)\nclient<llm> ClaudeSonnet {\n  provider \"anthropic\"\n  options {\n    model \"claude-3-5-sonnet-20241022\"\n    api_key env.ANTHROPIC_API_KEY\n    max_tokens 4000\n    temperature 0.1  // Low temperature for consistent analysis\n  }\n}\n\n// GPT-4o Mini - Fast classification for simple cases\nclient<llm> GPT4oMini {\n  provider \"openai\"\n  options {\n    model \"gpt-4o-mini\"\n    api_key env.OPENAI_API_KEY\n    max_tokens 2000\n    temperature 0.1\n  }\n}\n\n// =============================================================================\n// 🏛️ SPECIALIZED LEGAL MODELS (via HuggingFace)\n// =============================================================================\n\n// Legal-BERT for legal document analysis\nclient<llm> LegalBERT {\n  provider \"openai-generic\"\n  options {\n    base_url \"https://api-inference.huggingface.co/models/nlpaueb/legal-bert-base-uncased\"\n    api_key env.HUGGINGFACE_API_KEY\n    model \"nlpaueb/legal-bert-base-uncased\"\n    max_tokens 1000\n    temperature 0.0  // Deterministic for legal analysis\n  }\n}\n\n// RiskBERT for risk assessment\nclient<llm> RiskBERT {\n  provider \"openai-generic\"\n  options {\n    base_url \"https://api-inference.huggingface.co/models/ProsusAI/finbert\"\n    api_key env.HUGGINGFACE_API_KEY\n    model \"ProsusAI/finbert\"\n    max_tokens 1000\n    temperature 0.0\n  }\n}\n\n// FinBERT for financial analysis\nclient<llm> FinBERT {\n  provider \"openai-generic\"\n  options {\n    base_url \"https://api-inference.huggingface.co/models/ProsusAI/finbert\"\n    api_key env.HUGGINGFACE_API_KEY\n    model \"ProsusAI/finbert\"\n    max_tokens 1000\n    temperature 0.0\n  }\n}\n\n// =============================================================================\n// 🔄 FALLBACK AND RETRY STRATEGIES\n// =============================================================================\n\n// Primary classification client with fallback\nclient<llm> PrimaryClassifier {\n  provider \"fallback\"\n  options {\n    strategy [\n      GPT4o,\n      GPT4oMini,\n      ClaudeSonnet\n    ]\n  }\n}\n\n// High-accuracy consensus client\nclient<llm> ConsensusEngine {\n  provider \"round-robin\"\n  options {\n    strategy [\n      GPT4o,\n      GPT4oMini\n    ]\n  }\n}\n\n// =============================================================================\n// 🚀 PERFORMANCE-OPTIMIZED CLIENTS\n// =============================================================================\n\n// Fast client for simple classifications\nclient<llm> FastClassifier {\n  provider \"openai\"\n  options {\n    model \"gpt-4o-mini\"\n    api_key env.OPENAI_API_KEY\n    max_tokens 1000\n    temperature 0.0\n    timeout_ms 5000  // 5 second timeout for fast responses\n  }\n}\n\n// High-accuracy client for complex analysis\nclient<llm> DeepAnalyzer {\n  provider \"openai\"\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n    max_tokens 8000\n    temperature 0.05\n    timeout_ms 30000  // 30 second timeout for complex analysis\n  }\n}\n\n// =============================================================================\n// 🔒 SECURE CLIENTS FOR SENSITIVE DATA\n// =============================================================================\n\n// Secure client for PII and sensitive information\nclient<llm> SecureProcessor {\n  provider \"openai\"\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n    max_tokens 4000\n    temperature 0.0  // Deterministic for sensitive data\n    // Additional security headers can be added here\n  }\n}\n\n// =============================================================================\n// 🇨🇦 CANADIAN LEGAL SPECIALIST CLIENT\n// =============================================================================\n\n// Specialized client for Canadian legal analysis\nclient<llm> CanadianLegalExpert {\n  provider \"openai\"\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n    max_tokens 6000\n    temperature 0.1\n    // Optimized for Canadian legal context\n  }\n}\n\n// =============================================================================\n// 🎯 WORKFLOW-SPECIFIC CLIENTS\n// =============================================================================\n\n// Email processing client\nclient<llm> EmailProcessor {\n  provider \"fallback\"\n  retry_policy StandardRetry\n  options {\n    strategy [\n      GPT4o,\n      GPT4oMini\n    ]\n  }\n}\n\n// Document analysis client\nclient<llm> DocumentAnalyzer {\n  provider \"openai\"\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n    max_tokens 8000  // Large context for documents\n    temperature 0.1\n  }\n}\n\n// Policy verification client\nclient<llm> PolicyVerifier {\n  provider \"openai\"\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n    max_tokens 4000\n    temperature 0.0  // Deterministic for policy checks\n  }\n}\n\n// Financial assessment client\nclient<llm> FinancialAnalyzer {\n  provider \"round-robin\"\n  options {\n    strategy [\n      GPT4o,\n      FinBERT\n    ]\n  }\n}\n\n// =============================================================================\n// 🧪 TESTING AND DEVELOPMENT CLIENTS\n// =============================================================================\n\n// Test client for development\nclient<llm> TestClient {\n  provider \"openai\"\n  options {\n    model \"gpt-4o-mini\"\n    api_key env.TEST_OPENAI_API_KEY\n    max_tokens 1000\n    temperature 0.5\n  }\n}\n\n// Mock client for unit testing\nclient<llm> MockClient {\n  provider \"openai\"\n  options {\n    model \"gpt-4o-mini\"\n    api_key \"test-key\"\n    max_tokens 500\n    temperature 0.0\n    base_url \"http://localhost:8080/mock\"  // Mock server for testing\n  }\n}\n",
  "generators.baml": "// 🔧 BAML Generator Configuration for Zurich AI Claims Processing\n// Generates Python client code for the claims processing system\n\ngenerator ZurichClaimsClient {\n  output_type \"python/pydantic\"\n  output_dir \"../src/baml_client\"\n  version \"0.90.2\"\n}\n\n// Additional generator for TypeScript (if needed for frontend)\ngenerator ZurichClaimsClientTS {\n  output_type \"typescript\"\n  output_dir \"../frontend/src/baml_client\"\n  version \"0.90.2\"\n}\n",
  "main.baml": "// 🏆 Zurich AI Claims Processing - BAML Schemas\n// Structured AI outputs for email classification, claim detection, and document analysis\n\n// =============================================================================\n// 🎯 EMAIL CLASSIFICATION SCHEMAS\n// =============================================================================\n\nenum EmailType {\n  CLAIM_SUBMISSION\n  CLAIM_INQUIRY\n  POLICY_QUESTION\n  BILLING_INQUIRY\n  GENERAL_INQUIRY\n  SPAM\n  NOT_INSURANCE_RELATED\n}\n\nenum ClaimType {\n  AUTO_ACCIDENT\n  PROPERTY_DAMAGE\n  LIABILITY\n  PERSONAL_INJURY\n  THEFT\n  FIRE_DAMAGE\n  WATER_DAMAGE\n  VANDALISM\n  OTHER\n}\n\nenum UrgencyLevel {\n  LOW\n  MEDIUM\n  HIGH\n  CRITICAL\n}\n\nenum ConfidenceLevel {\n  VERY_LOW    // 0-20%\n  LOW         // 21-40%\n  MEDIUM      // 41-60%\n  HIGH        // 61-80%\n  VERY_HIGH   // 81-100%\n}\n\n// =============================================================================\n// 📧 EMAIL ANALYSIS RESULT\n// =============================================================================\n\nclass EmailAnalysis {\n  email_type EmailType @description(\"Primary classification of the email\")\n  is_claim bool @description(\"True if this email contains a claim submission or inquiry\")\n  claim_type ClaimType? @description(\"Type of claim if is_claim is true\")\n  urgency_level UrgencyLevel @description(\"Urgency level based on content analysis\")\n  confidence ConfidenceLevel @description(\"AI confidence in the classification\")\n  \n  // Key information extraction\n  policy_number string? @description(\"Insurance policy number if mentioned\")\n  claim_number string? @description(\"Existing claim number if referenced\")\n  incident_date string? @description(\"Date of incident in ISO format if mentioned\")\n  location string? @description(\"Location of incident if mentioned\")\n  \n  // Contact information\n  customer_name string? @description(\"Customer name extracted from email\")\n  customer_phone string? @description(\"Phone number if provided\")\n  customer_email string @description(\"Email address of sender\")\n  \n  // Content analysis\n  summary string @description(\"Brief summary of email content (max 200 chars)\")\n  key_details string[] @description(\"Important details extracted from email\")\n  attachments_mentioned bool @description(\"True if email mentions attachments\")\n  \n  // Decision flags\n  requires_human_review bool @description(\"True if human review is recommended\")\n  requires_immediate_action bool @description(\"True if immediate action is needed\")\n  \n  reasoning string @description(\"Explanation of classification decision\")\n}\n\n// =============================================================================\n// 📄 DOCUMENT ANALYSIS SCHEMAS\n// =============================================================================\n\nenum DocumentType {\n  POLICE_REPORT\n  MEDICAL_REPORT\n  REPAIR_ESTIMATE\n  PHOTO_EVIDENCE\n  INSURANCE_FORM\n  RECEIPT\n  INVOICE\n  WITNESS_STATEMENT\n  CORRESPONDENCE\n  OTHER_DOCUMENT\n}\n\nclass DocumentAnalysis {\n  document_type DocumentType @description(\"Type of document identified\")\n  confidence ConfidenceLevel @description(\"Confidence in document type classification\")\n  \n  // Extracted information\n  key_information string[] @description(\"Important information extracted from document\")\n  dates_mentioned string[] @description(\"Dates found in document (ISO format)\")\n  amounts_mentioned float[] @description(\"Monetary amounts found in document\")\n  \n  // Quality assessment\n  is_readable bool @description(\"True if document text is clearly readable\")\n  quality_score int @description(\"Document quality score 1-10\")\n  \n  // Relevance\n  is_relevant_to_claim bool @description(\"True if document is relevant to insurance claim\")\n  relevance_explanation string @description(\"Explanation of document relevance\")\n}\n\n// =============================================================================\n// 🔍 POLICY VERIFICATION SCHEMAS\n// =============================================================================\n\nenum CoverageStatus {\n  COVERED\n  NOT_COVERED\n  PARTIALLY_COVERED\n  REQUIRES_INVESTIGATION\n  POLICY_NOT_FOUND\n}\n\nclass PolicyVerification {\n  policy_found bool @description(\"True if policy was found in system\")\n  policy_active bool @description(\"True if policy is currently active\")\n  coverage_status CoverageStatus @description(\"Coverage status for the claim\")\n  \n  // Policy details\n  policy_type string? @description(\"Type of insurance policy\")\n  coverage_limits float? @description(\"Coverage limit amount\")\n  deductible float? @description(\"Policy deductible amount\")\n  \n  // Verification details\n  verification_notes string @description(\"Notes about policy verification\")\n  exclusions_apply bool @description(\"True if policy exclusions may apply\")\n  exclusion_details string[] @description(\"Details of applicable exclusions\")\n}\n\n// =============================================================================\n// ⚖️ LEGAL ASSESSMENT SCHEMAS\n// =============================================================================\n\nenum LegalRisk {\n  VERY_LOW\n  LOW\n  MEDIUM\n  HIGH\n  VERY_HIGH\n}\n\nenum LiabilityAssessment {\n  CLEAR_LIABILITY\n  DISPUTED_LIABILITY\n  NO_LIABILITY\n  REQUIRES_INVESTIGATION\n}\n\nclass LegalAnalysis {\n  legal_risk LegalRisk @description(\"Overall legal risk assessment\")\n  liability_assessment LiabilityAssessment @description(\"Liability determination\")\n  \n  // Canadian legal considerations\n  applicable_province string? @description(\"Canadian province where incident occurred\")\n  relevant_statutes string[] @description(\"Relevant Canadian statutes or regulations\")\n  precedent_cases string[] @description(\"Similar legal precedents if applicable\")\n  \n  // Risk factors\n  risk_factors string[] @description(\"Identified legal risk factors\")\n  mitigation_strategies string[] @description(\"Recommended risk mitigation strategies\")\n  \n  legal_notes string @description(\"Additional legal considerations\")\n}\n\n// =============================================================================\n// 💰 FINANCIAL ASSESSMENT SCHEMAS\n// =============================================================================\n\nclass FinancialAssessment {\n  estimated_claim_value float @description(\"Estimated total claim value in CAD\")\n  confidence_range_min float @description(\"Minimum estimated value\")\n  confidence_range_max float @description(\"Maximum estimated value\")\n  \n  // Cost breakdown\n  property_damage_estimate float? @description(\"Estimated property damage cost\")\n  medical_costs_estimate float? @description(\"Estimated medical costs\")\n  legal_costs_estimate float? @description(\"Estimated legal costs\")\n  other_costs_estimate float? @description(\"Other estimated costs\")\n  \n  // Financial flags\n  exceeds_authority_limit bool @description(\"True if claim exceeds adjuster authority\")\n  requires_reserves bool @description(\"True if financial reserves should be set\")\n  \n  financial_notes string @description(\"Additional financial considerations\")\n}\n\n// =============================================================================\n// 🎯 FINAL DECISION SCHEMAS\n// =============================================================================\n\nenum ClaimDecision {\n  APPROVE_PAYMENT\n  DENY_CLAIM\n  REQUIRES_INVESTIGATION\n  REQUIRES_HUMAN_APPROVAL\n  REQUEST_MORE_INFORMATION\n}\n\nenum NextAction {\n  CREATE_ZENDESK_TICKET\n  SCHEDULE_INSPECTION\n  REQUEST_DOCUMENTS\n  CONTACT_CUSTOMER\n  ESCALATE_TO_MANAGER\n  CLOSE_INQUIRY\n  TRANSFER_TO_SPECIALIST\n}\n\nclass ClaimRecommendation {\n  decision ClaimDecision @description(\"Recommended claim decision\")\n  next_actions NextAction[] @description(\"Recommended next actions\")\n  \n  // Priority and timing\n  priority_level UrgencyLevel @description(\"Processing priority level\")\n  target_response_time int @description(\"Target response time in hours\")\n  \n  // Human involvement\n  requires_human_approval bool @description(\"True if human approval is required\")\n  approval_reason string? @description(\"Reason why human approval is needed\")\n  recommended_approver string? @description(\"Recommended person/role for approval\")\n  \n  // Communication\n  customer_communication_required bool @description(\"True if customer should be contacted\")\n  communication_template string? @description(\"Suggested communication template\")\n  \n  // Workflow\n  zendesk_priority string @description(\"Suggested Zendesk ticket priority\")\n  zendesk_tags string[] @description(\"Suggested Zendesk tags\")\n  \n  recommendation_summary string @description(\"Summary of recommendation and reasoning\")\n}\n\n// =============================================================================\n// 🔄 MULTI-MODEL CONSENSUS SCHEMA\n// =============================================================================\n\nclass ModelConsensus {\n  claude_analysis EmailAnalysis @description(\"Analysis from Claude 3.5 Sonnet\")\n  gpt4_analysis EmailAnalysis @description(\"Analysis from GPT-4o\")\n  \n  // Consensus results\n  consensus_reached bool @description(\"True if models agree on classification\")\n  consensus_confidence ConfidenceLevel @description(\"Confidence in consensus\")\n  \n  // Disagreement handling\n  disagreement_areas string[] @description(\"Areas where models disagree\")\n  tie_breaker_needed bool @description(\"True if human tie-breaker is needed\")\n  \n  // Final consensus\n  final_analysis EmailAnalysis @description(\"Final consensus analysis\")\n  consensus_notes string @description(\"Notes about consensus process\")\n}\n\n// =============================================================================\n// 🤖 BAML FUNCTIONS FOR AI ANALYSIS\n// =============================================================================\n\n// Primary email classification function using GPT-4o\nfunction ClassifyEmail(\n  email_subject: string,\n  email_body: string,\n  sender_email: string,\n  attachments: string[]\n) -> EmailAnalysis {\n  client GPT4o\n  prompt #\"\n    You are an expert insurance claims analyst for Zurich Insurance, specializing in Canadian insurance law and claims processing.\n    You are powered by GPT-4o and provide highly accurate email classification and analysis.\n\n    Analyze the following email and provide a comprehensive classification and analysis.\n\n    {{ ctx.output_format }}\n\n    EMAIL DETAILS:\n    From: {{ sender_email }}\n    Subject: {{ email_subject }}\n    Attachments: {{ attachments | join(\", \") if attachments else \"None\" }}\n\n    Body:\n    {{ email_body }}\n\n    ANALYSIS GUIDELINES:\n\n    1. EMAIL CLASSIFICATION:\n       - Determine if this is a claim submission, inquiry, or other type\n       - Assess urgency based on content (keywords like \"urgent\", \"emergency\", injury mentions)\n       - Look for policy/claim numbers, dates, locations\n\n    2. CLAIM DETECTION:\n       - Look for incident descriptions, damage reports, injury mentions\n       - Identify claim types: auto, property, liability, etc.\n       - Extract incident dates and locations\n\n    3. INFORMATION EXTRACTION:\n       - Extract policy numbers (format: various patterns)\n       - Extract claim numbers if referenced\n       - Extract customer contact information\n       - Identify key details and dates\n\n    4. DECISION FLAGS:\n       - Requires human review: complex cases, high values, legal issues\n       - Immediate action: injuries, emergencies, time-sensitive matters\n\n    5. CONFIDENCE ASSESSMENT:\n       - VERY_HIGH (81-100%): Clear, unambiguous classification\n       - HIGH (61-80%): Strong indicators, minor ambiguity\n       - MEDIUM (41-60%): Some uncertainty, mixed signals\n       - LOW (21-40%): Weak indicators, significant ambiguity\n       - VERY_LOW (0-20%): Very unclear, insufficient information\n\n    Provide detailed reasoning for your classification decisions.\n  \"#\n}\n\n// Validation function using GPT-4o Mini for consensus\nfunction ValidateEmailClassification(\n  email_subject: string,\n  email_body: string,\n  sender_email: string,\n  attachments: string[]\n) -> EmailAnalysis {\n  client GPT4oMini\n  prompt #\"\n    You are a senior insurance claims validator for Zurich Insurance, providing second-opinion analysis on email classifications.\n    You are powered by GPT-4o Mini for fast and efficient validation.\n\n    Analyze this email independently and provide your classification.\n\n    {{ ctx.output_format }}\n\n    EMAIL DETAILS:\n    From: {{ sender_email }}\n    Subject: {{ email_subject }}\n    Attachments: {{ attachments | join(\", \") if attachments else \"None\" }}\n\n    Body:\n    {{ email_body }}\n\n    VALIDATION FOCUS:\n\n    1. Independent classification without bias\n    2. Focus on Canadian insurance context\n    3. Identify any red flags or unusual patterns\n    4. Assess information completeness\n    5. Evaluate urgency and priority levels\n\n    Provide thorough analysis with clear reasoning.\n  \"#\n}\n\n// Document analysis function\nfunction AnalyzeDocument(\n  document_text: string,\n  document_filename: string,\n  claim_context: string\n) -> DocumentAnalysis {\n  client GPT4o\n  prompt #\"\n    You are a document analysis specialist for insurance claims processing.\n\n    Analyze the following document in the context of an insurance claim.\n\n    {{ ctx.output_format }}\n\n    DOCUMENT DETAILS:\n    Filename: {{ document_filename }}\n    Claim Context: {{ claim_context }}\n\n    Document Content:\n    {{ document_text }}\n\n    ANALYSIS REQUIREMENTS:\n\n    1. Document Type Classification:\n       - Identify the type of document (police report, medical report, etc.)\n       - Assess document authenticity indicators\n\n    2. Information Extraction:\n       - Extract key facts, dates, amounts\n       - Identify relevant parties and contacts\n       - Note any inconsistencies or concerns\n\n    3. Quality Assessment:\n       - Evaluate readability and completeness\n       - Rate document quality (1-10 scale)\n       - Identify missing information\n\n    4. Claim Relevance:\n       - Determine relevance to insurance claim\n       - Identify supporting or contradicting evidence\n       - Note any red flags or concerns\n\n    Provide detailed analysis with specific examples from the document.\n  \"#\n}\n\n// Policy verification function\nfunction VerifyPolicyDetails(\n  policy_number: string,\n  incident_date: string,\n  claim_type: string,\n  policy_data: string\n) -> PolicyVerification {\n  client PolicyVerifier\n  prompt #\"\n    You are a policy verification specialist for Zurich Insurance.\n\n    Verify policy coverage for the given claim details.\n\n    {{ ctx.output_format }}\n\n    VERIFICATION REQUEST:\n    Policy Number: {{ policy_number }}\n    Incident Date: {{ incident_date }}\n    Claim Type: {{ claim_type }}\n\n    Policy Data:\n    {{ policy_data }}\n\n    VERIFICATION CHECKLIST:\n\n    1. Policy Status:\n       - Confirm policy is active on incident date\n       - Check for any lapses or cancellations\n       - Verify premium payment status\n\n    2. Coverage Analysis:\n       - Determine if claim type is covered\n       - Check coverage limits and deductibles\n       - Identify any applicable exclusions\n\n    3. Exclusion Review:\n       - Review policy exclusions\n       - Assess if any exclusions apply to this claim\n       - Note any grey areas requiring investigation\n\n    4. Special Conditions:\n       - Check for any special policy conditions\n       - Review endorsements or riders\n       - Note any coverage modifications\n\n    Provide clear coverage determination with supporting details.\n  \"#\n}\n\n// Legal risk assessment function\nfunction AssessLegalRisk(\n  incident_description: string,\n  location: string,\n  parties_involved: string,\n  claim_value: float\n) -> LegalAnalysis {\n  client GPT4o\n  prompt #\"\n    You are a legal risk analyst specializing in Canadian insurance law.\n\n    Assess the legal risk and liability for this insurance claim.\n\n    {{ ctx.output_format }}\n\n    CLAIM DETAILS:\n    Incident: {{ incident_description }}\n    Location: {{ location }}\n    Parties: {{ parties_involved }}\n    Estimated Value: ${{ claim_value }} CAD\n\n    LEGAL ANALYSIS FRAMEWORK:\n\n    1. Liability Assessment:\n       - Determine clear, disputed, or no liability\n       - Identify contributing factors\n       - Assess comparative negligence\n\n    2. Canadian Legal Context:\n       - Identify applicable provincial laws\n       - Reference relevant statutes and regulations\n       - Consider tort law principles\n\n    3. Risk Factors:\n       - Identify potential legal complications\n       - Assess litigation probability\n       - Evaluate settlement vs. defense strategy\n\n    4. Precedent Analysis:\n       - Reference similar Canadian cases\n       - Note relevant legal precedents\n       - Consider recent legal developments\n\n    5. Risk Mitigation:\n       - Recommend risk mitigation strategies\n       - Suggest early intervention opportunities\n       - Identify expert witness needs\n\n    Provide comprehensive legal risk assessment with Canadian law focus.\n  \"#\n}\n",
  "tests.baml": "// 🧪 BAML Test Cases for Zurich AI Claims Processing\n// Comprehensive test suite for email classification and analysis functions\n\n// =============================================================================\n// 📧 EMAIL CLASSIFICATION TESTS\n// =============================================================================\n\ntest ClaimSubmissionTest {\n  functions [ClassifyEmail]\n  args {\n    email_subject \"Car Accident Claim - Policy #ZUR123456\"\n    email_body \"Hi, I was involved in a car accident yesterday at the intersection of King St and Queen St in Toronto. The other driver ran a red light and hit my vehicle. My car has significant damage to the front end and I need to file a claim. My policy number is ZUR123456. Please let me know what documents you need. Thanks, John Smith\"\n    sender_email \"<EMAIL>\"\n    attachments [\"accident_photos.jpg\", \"police_report.pdf\"]\n  }\n}\n\ntest PolicyInquiryTest {\n  functions [ClassifyEmail]\n  args {\n    email_subject \"Question about my coverage\"\n    email_body \"Hello, I have a question about what's covered under my home insurance policy. Specifically, I want to know if water damage from a burst pipe would be covered. My policy number is ZUR789012. Thank you.\"\n    sender_email \"<EMAIL>\"\n    attachments []\n  }\n}\n\ntest SpamEmailTest {\n  functions [ClassifyEmail]\n  args {\n    email_subject \"URGENT: You've won $1,000,000!!!\"\n    email_body \"Congratulations! You have been selected to receive $1,000,000 in our lottery. Click here to claim your prize now! This offer expires in 24 hours.\"\n    sender_email \"<EMAIL>\"\n    attachments []\n  }\n}\n\ntest UrgentClaimTest {\n  functions [ClassifyEmail]\n  args {\n    email_subject \"URGENT - House Fire Claim\"\n    email_body \"URGENT: Our house caught fire last night and we lost everything. We need immediate assistance. The fire department said it was caused by electrical issues. We have nowhere to stay and need help with temporary accommodation. Policy: ZUR456789. Please call me immediately at ************.\"\n    sender_email \"<EMAIL>\"\n    attachments [\"fire_damage_photos.zip\", \"fire_department_report.pdf\"]\n  }\n}\n\ntest LiabilityClaimTest {\n  functions [ClassifyEmail]\n  args {\n    email_subject \"Liability Claim - Slip and Fall\"\n    email_body \"I am writing to report an incident that occurred at my business premises. A customer slipped and fell on wet floors and is claiming injury. They are threatening to sue. I need to file a liability claim under my business insurance. Policy number: ZUR321654. The incident happened on June 25, 2025 at approximately 2:30 PM.\"\n    sender_email \"<EMAIL>\"\n    attachments [\"incident_report.pdf\", \"witness_statements.docx\"]\n  }\n}\n\ntest IncompleteClaimTest {\n  functions [ClassifyEmail]\n  args {\n    email_subject \"Claim\"\n    email_body \"I need to file a claim. Something happened to my car.\"\n    sender_email \"<EMAIL>\"\n    attachments []\n  }\n}\n\n// =============================================================================\n// 🔄 CONSENSUS VALIDATION TESTS\n// =============================================================================\n\ntest ConsensusValidationTest {\n  functions [ValidateEmailClassification]\n  args {\n    email_subject \"Auto Accident - Need Help\"\n    email_body \"Hi, I was in a car accident this morning. The other driver was texting and rear-ended me at a red light. My neck hurts and my car is damaged. I think I need to go to the hospital. My policy is ZUR555777. What should I do?\"\n    sender_email \"<EMAIL>\"\n    attachments [\"accident_scene.jpg\"]\n  }\n}\n\n// =============================================================================\n// 📄 DOCUMENT ANALYSIS TESTS\n// =============================================================================\n\ntest PoliceReportAnalysisTest {\n  functions [AnalyzeDocument]\n  args {\n    document_text \"POLICE REPORT - MOTOR VEHICLE ACCIDENT\\nReport Number: TR-2025-001234\\nDate: June 28, 2025\\nTime: 14:30\\nLocation: King St W & Queen St, Toronto, ON\\n\\nVehicle 1: 2020 Honda Civic, License: ABCD123\\nDriver: John Smith, DOB: 1985-03-15\\nInsurance: Zurich Insurance, Policy: ZUR123456\\n\\nVehicle 2: 2018 Ford F-150, License: XYZ789\\nDriver: Mike Johnson, DOB: 1978-11-22\\nInsurance: State Farm, Policy: SF987654\\n\\nDescription: Vehicle 2 failed to stop at red light and collided with Vehicle 1 which was proceeding through intersection on green light. Vehicle 1 sustained damage to front end. No injuries reported at scene.\\n\\nOfficer: Constable Sarah Wilson, Badge #4567\\nCharges: Vehicle 2 driver charged with running red light under HTA Section 144(18)\"\n    document_filename \"police_report_TR-2025-001234.pdf\"\n    claim_context \"Auto accident claim for policy ZUR123456, incident on June 28, 2025\"\n  }\n}\n\ntest MedicalReportAnalysisTest {\n  functions [AnalyzeDocument]\n  args {\n    document_text \"MEDICAL REPORT\\nPatient: John Smith\\nDOB: 1985-03-15\\nDate of Service: June 28, 2025\\n\\nChief Complaint: Neck pain following motor vehicle accident\\n\\nHistory: Patient reports being involved in rear-end collision earlier today. Complains of neck stiffness and headache. No loss of consciousness. Wearing seatbelt at time of impact.\\n\\nExamination: Alert and oriented. Cervical spine tender to palpation. Range of motion limited due to pain. No neurological deficits noted.\\n\\nDiagnosis: Cervical strain (whiplash)\\n\\nTreatment: Prescribed muscle relaxants and physiotherapy. Follow-up in 1 week.\\n\\nWork Status: Off work for 3 days, then light duties for 2 weeks.\\n\\nDr. Emily Chen, MD\\nToronto General Hospital\"\n    document_filename \"medical_report_john_smith.pdf\"\n    claim_context \"Personal injury component of auto accident claim ZUR123456\"\n  }\n}\n\n// =============================================================================\n// 🏛️ POLICY VERIFICATION TESTS\n// =============================================================================\n\ntest PolicyVerificationActiveTest {\n  functions [VerifyPolicyDetails]\n  args {\n    policy_number \"ZUR123456\"\n    incident_date \"2025-06-28\"\n    claim_type \"AUTO_ACCIDENT\"\n    policy_data \"Policy Number: ZUR123456\\nPolicyholder: John Smith\\nPolicy Type: Auto Insurance\\nEffective Date: 2024-01-01\\nExpiry Date: 2025-12-31\\nPremium Status: Paid\\nCoverage: Comprehensive and Collision\\nDeductible: $500\\nLiability Limit: $1,000,000\\nProperty Damage: $500,000\\nMedical Payments: $50,000\\nUninsured Motorist: $1,000,000\"\n  }\n}\n\ntest PolicyVerificationExpiredTest {\n  functions [VerifyPolicyDetails]\n  args {\n    policy_number \"ZUR999888\"\n    incident_date \"2025-06-28\"\n    claim_type \"AUTO_ACCIDENT\"\n    policy_data \"Policy Number: ZUR999888\\nPolicyholder: Jane Doe\\nPolicy Type: Auto Insurance\\nEffective Date: 2023-01-01\\nExpiry Date: 2024-12-31\\nPremium Status: Lapsed\\nCoverage: Liability Only\\nNote: Policy cancelled for non-payment on 2024-11-15\"\n  }\n}\n\n// =============================================================================\n// ⚖️ LEGAL RISK ASSESSMENT TESTS\n// =============================================================================\n\ntest LegalRiskLowTest {\n  functions [AssessLegalRisk]\n  args {\n    incident_description \"Minor fender bender in parking lot. Other driver admitted fault and apologized. No injuries. Minimal property damage.\"\n    location \"Toronto, Ontario\"\n    parties_involved \"Insured: John Smith, Third Party: Mike Johnson (admitted fault)\"\n    claim_value 2500.0\n  }\n}\n\ntest LegalRiskHighTest {\n  functions [AssessLegalRisk]\n  args {\n    incident_description \"Multi-vehicle accident on Highway 401. Disputed liability. Serious injuries reported. Potential fatality. Commercial vehicle involved.\"\n    location \"Toronto, Ontario\"\n    parties_involved \"Insured: ABC Transport Ltd, Third Parties: Multiple private vehicles, Pedestrian injured\"\n    claim_value 2500000.0\n  }\n}\n\n// =============================================================================\n// 🇨🇦 CANADIAN LEGAL CONTEXT TESTS\n// =============================================================================\n\ntest CanadianLegalContextTest {\n  functions [AssessLegalRisk]\n  args {\n    incident_description \"Slip and fall at retail store. Customer claims wet floor was not properly marked. Seeking damages for lost wages and medical expenses.\"\n    location \"Vancouver, British Columbia\"\n    parties_involved \"Insured: Retail Store Inc, Claimant: Customer with back injury\"\n    claim_value 75000.0\n  }\n}\n\n// =============================================================================\n// 🔄 EDGE CASE TESTS\n// =============================================================================\n\ntest EmptyEmailTest {\n  functions [ClassifyEmail]\n  args {\n    email_subject \"\"\n    email_body \"\"\n    sender_email \"<EMAIL>\"\n    attachments []\n  }\n}\n\ntest NonEnglishEmailTest {\n  functions [ClassifyEmail]\n  args {\n    email_subject \"Réclamation d'assurance automobile\"\n    email_body \"Bonjour, j'ai eu un accident de voiture hier. Ma police d'assurance est ZUR123456. Pouvez-vous m'aider?\"\n    sender_email \"<EMAIL>\"\n    attachments []\n  }\n}\n\ntest VeryLongEmailTest {\n  functions [ClassifyEmail]\n  args {\n    email_subject \"Detailed Claim Report\"\n    email_body \"This is a very long email with extensive details about an incident that occurred... [This would be a very long email body with multiple paragraphs describing a complex claim scenario with many details, parties involved, timeline of events, etc.]\"\n    sender_email \"<EMAIL>\"\n    attachments [\"document1.pdf\", \"document2.pdf\", \"photos.zip\", \"statements.docx\"]\n  }\n}\n",
}
export const getBamlFiles = () => {
    return fileMap;
}