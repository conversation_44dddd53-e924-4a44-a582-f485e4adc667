/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: do not edit it. Instead, edit the BAML
// files and re-generate this code.
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
import type { Image, Audio } from "@boundaryml/baml"

/**
 * Recursively partial type that can be null.
 *
 * @deprecated Use types from the `partial_types` namespace instead, which provides type-safe partial implementations
 * @template T The type to make recursively partial.
 */
export type RecursivePartialNull<T> = T extends object
    ? { [P in keyof T]?: RecursivePartialNull<T[P]> }
    : T | null;

export interface Checked<T,CheckName extends string = string> {
    value: T,
    checks: Record<CheckName, Check>,
}


export interface Check {
    name: string,
    expr: string
    status: "succeeded" | "failed"
}

export function all_succeeded<CheckName extends string>(checks: Record<CheckName, Check>): boolean {
    return get_checks(checks).every(check => check.status === "succeeded")
}

export function get_checks<CheckName extends string>(checks: Record<CheckName, Check>): Check[] {
    return Object.values(checks)
}
export enum ClaimDecision {
  APPROVE_PAYMENT = "APPROVE_PAYMENT",
  DENY_CLAIM = "DENY_CLAIM",
  REQUIRES_INVESTIGATION = "REQUIRES_INVESTIGATION",
  REQUIRES_HUMAN_APPROVAL = "REQUIRES_HUMAN_APPROVAL",
  REQUEST_MORE_INFORMATION = "REQUEST_MORE_INFORMATION",
}

export enum ClaimType {
  AUTO_ACCIDENT = "AUTO_ACCIDENT",
  PROPERTY_DAMAGE = "PROPERTY_DAMAGE",
  LIABILITY = "LIABILITY",
  PERSONAL_INJURY = "PERSONAL_INJURY",
  THEFT = "THEFT",
  FIRE_DAMAGE = "FIRE_DAMAGE",
  WATER_DAMAGE = "WATER_DAMAGE",
  VANDALISM = "VANDALISM",
  OTHER = "OTHER",
}

export enum ConfidenceLevel {
  VERY_LOW = "VERY_LOW",
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  VERY_HIGH = "VERY_HIGH",
}

export enum CoverageStatus {
  COVERED = "COVERED",
  NOT_COVERED = "NOT_COVERED",
  PARTIALLY_COVERED = "PARTIALLY_COVERED",
  REQUIRES_INVESTIGATION = "REQUIRES_INVESTIGATION",
  POLICY_NOT_FOUND = "POLICY_NOT_FOUND",
}

export enum DocumentType {
  POLICE_REPORT = "POLICE_REPORT",
  MEDICAL_REPORT = "MEDICAL_REPORT",
  REPAIR_ESTIMATE = "REPAIR_ESTIMATE",
  PHOTO_EVIDENCE = "PHOTO_EVIDENCE",
  INSURANCE_FORM = "INSURANCE_FORM",
  RECEIPT = "RECEIPT",
  INVOICE = "INVOICE",
  WITNESS_STATEMENT = "WITNESS_STATEMENT",
  CORRESPONDENCE = "CORRESPONDENCE",
  OTHER_DOCUMENT = "OTHER_DOCUMENT",
}

export enum EmailType {
  CLAIM_SUBMISSION = "CLAIM_SUBMISSION",
  CLAIM_INQUIRY = "CLAIM_INQUIRY",
  POLICY_QUESTION = "POLICY_QUESTION",
  BILLING_INQUIRY = "BILLING_INQUIRY",
  GENERAL_INQUIRY = "GENERAL_INQUIRY",
  SPAM = "SPAM",
  NOT_INSURANCE_RELATED = "NOT_INSURANCE_RELATED",
}

export enum LegalRisk {
  VERY_LOW = "VERY_LOW",
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  VERY_HIGH = "VERY_HIGH",
}

export enum LiabilityAssessment {
  CLEAR_LIABILITY = "CLEAR_LIABILITY",
  DISPUTED_LIABILITY = "DISPUTED_LIABILITY",
  NO_LIABILITY = "NO_LIABILITY",
  REQUIRES_INVESTIGATION = "REQUIRES_INVESTIGATION",
}

export enum NextAction {
  CREATE_ZENDESK_TICKET = "CREATE_ZENDESK_TICKET",
  SCHEDULE_INSPECTION = "SCHEDULE_INSPECTION",
  REQUEST_DOCUMENTS = "REQUEST_DOCUMENTS",
  CONTACT_CUSTOMER = "CONTACT_CUSTOMER",
  ESCALATE_TO_MANAGER = "ESCALATE_TO_MANAGER",
  CLOSE_INQUIRY = "CLOSE_INQUIRY",
  TRANSFER_TO_SPECIALIST = "TRANSFER_TO_SPECIALIST",
}

export enum UrgencyLevel {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

export interface ClaimRecommendation {
  decision: ClaimDecision
  next_actions: NextAction[]
  priority_level: UrgencyLevel
  target_response_time: number
  requires_human_approval: boolean
  approval_reason?: string | null
  recommended_approver?: string | null
  customer_communication_required: boolean
  communication_template?: string | null
  zendesk_priority: string
  zendesk_tags: string[]
  recommendation_summary: string
  
}

export interface DocumentAnalysis {
  document_type: DocumentType
  confidence: ConfidenceLevel
  key_information: string[]
  dates_mentioned: string[]
  amounts_mentioned: number[]
  is_readable: boolean
  quality_score: number
  is_relevant_to_claim: boolean
  relevance_explanation: string
  
}

export interface EmailAnalysis {
  email_type: EmailType
  is_claim: boolean
  claim_type?: ClaimType | null
  urgency_level: UrgencyLevel
  confidence: ConfidenceLevel
  policy_number?: string | null
  claim_number?: string | null
  incident_date?: string | null
  location?: string | null
  customer_name?: string | null
  customer_phone?: string | null
  customer_email: string
  summary: string
  key_details: string[]
  attachments_mentioned: boolean
  requires_human_review: boolean
  requires_immediate_action: boolean
  reasoning: string
  
}

export interface FinancialAssessment {
  estimated_claim_value: number
  confidence_range_min: number
  confidence_range_max: number
  property_damage_estimate?: number | null
  medical_costs_estimate?: number | null
  legal_costs_estimate?: number | null
  other_costs_estimate?: number | null
  exceeds_authority_limit: boolean
  requires_reserves: boolean
  financial_notes: string
  
}

export interface LegalAnalysis {
  legal_risk: LegalRisk
  liability_assessment: LiabilityAssessment
  applicable_province?: string | null
  relevant_statutes: string[]
  precedent_cases: string[]
  risk_factors: string[]
  mitigation_strategies: string[]
  legal_notes: string
  
}

export interface ModelConsensus {
  claude_analysis: EmailAnalysis
  gpt4_analysis: EmailAnalysis
  consensus_reached: boolean
  consensus_confidence: ConfidenceLevel
  disagreement_areas: string[]
  tie_breaker_needed: boolean
  final_analysis: EmailAnalysis
  consensus_notes: string
  
}

export interface PolicyVerification {
  policy_found: boolean
  policy_active: boolean
  coverage_status: CoverageStatus
  policy_type?: string | null
  coverage_limits?: number | null
  deductible?: number | null
  verification_notes: string
  exclusions_apply: boolean
  exclusion_details: string[]
  
}
