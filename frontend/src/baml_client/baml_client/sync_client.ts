/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: do not edit it. Instead, edit the BAML
// files and re-generate this code.
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
import type { Ba<PERSON>l<PERSON>unt<PERSON>, FunctionResult, BamlCtxManager, Image, Audio, ClientRegistry, Collector } from "@boundaryml/baml"
import { toBamlError, type HTTPRequest } from "@boundaryml/baml"
import type { Checked, Check, RecursivePartialNull as MovedRecursivePartialNull } from "./types"
import type * as types from "./types"
import type {ClaimDecision, ClaimRecommendation, ClaimType, ConfidenceLevel, CoverageStatus, DocumentAnalysis, DocumentType, EmailAnalysis, EmailType, FinancialAssessment, LegalAnalysis, LegalRisk, LiabilityAssessment, ModelConsensus, NextAction, PolicyVerification, UrgencyLevel} from "./types"
import type TypeBuilder from "./type_builder"
import { HttpRequest, HttpStreamRequest } from "./sync_request"
import { LlmResponseParser, LlmStreamParser } from "./parser"
import { DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME } from "./globals"

/**
 * @deprecated Use RecursivePartialNull from 'baml_client/types' instead.
 * Example:
 * ```ts
 * import { RecursivePartialNull } from './baml_client/types'
 * ```
 */
export type RecursivePartialNull<T> = MovedRecursivePartialNull<T>;

type BamlCallOptions = {
  tb?: TypeBuilder
  clientRegistry?: ClientRegistry
  collector?: Collector | Collector[]
  env?: Record<string, string | undefined>
}

export class BamlSyncClient {
  private httpRequest: HttpRequest
  private httpStreamRequest: HttpStreamRequest
  private llmResponseParser: LlmResponseParser
  private llmStreamParser: LlmStreamParser
  private bamlOptions: BamlCallOptions

  constructor(private runtime: BamlRuntime, private ctxManager: BamlCtxManager, private bamlOptions?: BamlCallOptions) {
    this.httpRequest = new HttpRequest(runtime, ctxManager)
    this.httpStreamRequest = new HttpStreamRequest(runtime, ctxManager)
    this.llmResponseParser = new LlmResponseParser(runtime, ctxManager)
    this.llmStreamParser = new LlmStreamParser(runtime, ctxManager)
    this.bamlOptions = bamlOptions || {}
  }

  withOptions(bamlOptions: BamlCallOptions) {
    return new BamlSyncClient(this.runtime, this.ctxManager, bamlOptions)
  }

  /*
  * @deprecated NOT IMPLEMENTED as streaming must by async. We
  * are not providing an async version as we want to reserve the
  * right to provide a sync version in the future.
  */
  get stream() {
    throw new Error("stream is not available in BamlSyncClient. Use `import { b } from 'baml_client/async_client")
  }

  get request() {
    return this.httpRequest
  }

  get streamRequest() {
    return this.httpStreamRequest
  }

  get parse() {
    return this.llmResponseParser
  }

  get parseStream() {
    return this.llmStreamParser
  }

  
  AnalyzeDocument(
      document_text: string,document_filename: string,claim_context: string,
      __baml_options__?: BamlCallOptions
  ): DocumentAnalysis {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.callFunctionSync(
        "AnalyzeDocument",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_context": claim_context
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as DocumentAnalysis
    } catch (error: any) {
      throw toBamlError(error);
    }
  }
  
  AssessLegalRisk(
      incident_description: string,location: string,parties_involved: string,claim_value: number,
      __baml_options__?: BamlCallOptions
  ): LegalAnalysis {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.callFunctionSync(
        "AssessLegalRisk",
        {
          "incident_description": incident_description,"location": location,"parties_involved": parties_involved,"claim_value": claim_value
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as LegalAnalysis
    } catch (error: any) {
      throw toBamlError(error);
    }
  }
  
  ClassifyEmail(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: BamlCallOptions
  ): EmailAnalysis {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.callFunctionSync(
        "ClassifyEmail",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as EmailAnalysis
    } catch (error: any) {
      throw toBamlError(error);
    }
  }
  
  ValidateEmailClassification(
      email_subject: string,email_body: string,sender_email: string,attachments: string[],
      __baml_options__?: BamlCallOptions
  ): EmailAnalysis {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.callFunctionSync(
        "ValidateEmailClassification",
        {
          "email_subject": email_subject,"email_body": email_body,"sender_email": sender_email,"attachments": attachments
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as EmailAnalysis
    } catch (error: any) {
      throw toBamlError(error);
    }
  }
  
  VerifyPolicyDetails(
      policy_number: string,incident_date: string,claim_type: string,policy_data: string,
      __baml_options__?: BamlCallOptions
  ): PolicyVerification {
    try {
      const options = { ...this.bamlOptions, ...(__baml_options__ || {}) }
      const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
      const env = options.env ? { ...process.env, ...options.env } : { ...process.env };
      const raw = this.runtime.callFunctionSync(
        "VerifyPolicyDetails",
        {
          "policy_number": policy_number,"incident_date": incident_date,"claim_type": claim_type,"policy_data": policy_data
        },
        this.ctxManager.cloneContext(),
        options.tb?.__tb(),
        options.clientRegistry,
        collector,
        env,
      )
      return raw.parsed(false) as PolicyVerification
    } catch (error: any) {
      throw toBamlError(error);
    }
  }
  
}

export const b = new BamlSyncClient(DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX)