/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: do not edit it. Instead, edit the BAML
// files and re-generate this code.
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
import type { Image, Audio } from "@boundaryml/baml"
import type { Checked, Check } from "./types"
import type {  ClaimDecision,  ClaimRecommendation,  ClaimType,  ConfidenceLevel,  CoverageStatus,  DocumentAnalysis,  DocumentType,  EmailAnalysis,  EmailType,  FinancialAssessment,  LegalAnalysis,  LegalRisk,  LiabilityAssessment,  ModelConsensus,  NextAction,  PolicyVerification,  UrgencyLevel } from "./types"
import type * as types from "./types"

/******************************************************************************
*
*  These types are used for streaming, for when an instance of a type
*  is still being built up and any of its fields is not yet fully available.
*
******************************************************************************/

export interface StreamState<T> {
    value: T
    state: "Pending" | "Incomplete" | "Complete"
}

export namespace partial_types {
    
    export interface ClaimRecommendation {
        decision?: (ClaimDecision | null)
        next_actions?: (NextAction | null)[]
        priority_level?: (UrgencyLevel | null)
        target_response_time?: (number | null)
        requires_human_approval?: (boolean | null)
        approval_reason: ((string | null) | null)
        recommended_approver: ((string | null) | null)
        customer_communication_required?: (boolean | null)
        communication_template: ((string | null) | null)
        zendesk_priority?: (string | null)
        zendesk_tags?: (string | null)[]
        recommendation_summary?: (string | null)
    }
    
    export interface DocumentAnalysis {
        document_type?: (DocumentType | null)
        confidence?: (ConfidenceLevel | null)
        key_information?: (string | null)[]
        dates_mentioned?: (string | null)[]
        amounts_mentioned?: (number | null)[]
        is_readable?: (boolean | null)
        quality_score?: (number | null)
        is_relevant_to_claim?: (boolean | null)
        relevance_explanation?: (string | null)
    }
    
    export interface EmailAnalysis {
        email_type?: (EmailType | null)
        is_claim?: (boolean | null)
        claim_type: ((ClaimType | null) | null)
        urgency_level?: (UrgencyLevel | null)
        confidence?: (ConfidenceLevel | null)
        policy_number: ((string | null) | null)
        claim_number: ((string | null) | null)
        incident_date: ((string | null) | null)
        location: ((string | null) | null)
        customer_name: ((string | null) | null)
        customer_phone: ((string | null) | null)
        customer_email?: (string | null)
        summary?: (string | null)
        key_details?: (string | null)[]
        attachments_mentioned?: (boolean | null)
        requires_human_review?: (boolean | null)
        requires_immediate_action?: (boolean | null)
        reasoning?: (string | null)
    }
    
    export interface FinancialAssessment {
        estimated_claim_value?: (number | null)
        confidence_range_min?: (number | null)
        confidence_range_max?: (number | null)
        property_damage_estimate: ((number | null) | null)
        medical_costs_estimate: ((number | null) | null)
        legal_costs_estimate: ((number | null) | null)
        other_costs_estimate: ((number | null) | null)
        exceeds_authority_limit?: (boolean | null)
        requires_reserves?: (boolean | null)
        financial_notes?: (string | null)
    }
    
    export interface LegalAnalysis {
        legal_risk?: (LegalRisk | null)
        liability_assessment?: (LiabilityAssessment | null)
        applicable_province: ((string | null) | null)
        relevant_statutes?: (string | null)[]
        precedent_cases?: (string | null)[]
        risk_factors?: (string | null)[]
        mitigation_strategies?: (string | null)[]
        legal_notes?: (string | null)
    }
    
    export interface ModelConsensus {
        claude_analysis?: (partial_types.EmailAnalysis | null)
        gpt4_analysis?: (partial_types.EmailAnalysis | null)
        consensus_reached?: (boolean | null)
        consensus_confidence?: (ConfidenceLevel | null)
        disagreement_areas?: (string | null)[]
        tie_breaker_needed?: (boolean | null)
        final_analysis?: (partial_types.EmailAnalysis | null)
        consensus_notes?: (string | null)
    }
    
    export interface PolicyVerification {
        policy_found?: (boolean | null)
        policy_active?: (boolean | null)
        coverage_status?: (CoverageStatus | null)
        policy_type: ((string | null) | null)
        coverage_limits: ((number | null) | null)
        deductible: ((number | null) | null)
        verification_notes?: (string | null)
        exclusions_apply?: (boolean | null)
        exclusion_details?: (string | null)[]
    }
    
}