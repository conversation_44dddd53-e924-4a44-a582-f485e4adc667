#!/usr/bin/env python3
"""
🧪 Test script for Multi-Model AI Classifier

This script tests the GPT-4o powered multi-model classifier to ensure
it's working correctly with the updated BAML configuration.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from email_processing.multi_model_classifier import MultiModelClassifier, ConsensusStrategy
from config.settings import Settings


async def test_classifier():
    """Test the multi-model classifier with a sample email"""
    
    print("🧪 Testing Multi-Model AI Classifier with GPT-4o")
    print("=" * 60)
    
    # Initialize settings
    settings = Settings()
    
    # Initialize classifier
    classifier = MultiModelClassifier(settings)
    
    # Test email data
    test_email = {
        "subject": "Car accident claim - urgent assistance needed",
        "body": """
        Dear Zurich Insurance,
        
        I was involved in a car accident yesterday evening at the intersection 
        of Main Street and Oak Avenue. The other driver ran a red light and 
        hit my vehicle on the passenger side. 
        
        I have photos of the damage and a police report number: PR-2024-001234.
        My policy number is ZUR-AUTO-789456123.
        
        The damage appears significant and I need to get my car towed and 
        repaired as soon as possible. Please let me know what steps I need 
        to take to file this claim.
        
        Thank you,
        <PERSON>
        Phone: (*************
        """,
        "sender": "<EMAIL>",
        "attachments": ["accident_photos.zip", "police_report.pdf"]
    }
    
    try:
        print(f"📧 Processing test email from: {test_email['sender']}")
        print(f"📋 Subject: {test_email['subject']}")
        print(f"📎 Attachments: {', '.join(test_email['attachments'])}")
        print()
        
        # Classify the email
        result = await classifier.classify_email(
            email_subject=test_email["subject"],
            email_body=test_email["body"],
            sender_email=test_email["sender"],
            attachments=test_email["attachments"],
            strategy=ConsensusStrategy.CONFIDENCE_WEIGHTED
        )
        
        print("✅ Classification Results:")
        print(f"   📊 Email Type: {result.final_analysis.email_type}")
        print(f"   🎯 Confidence: {result.consensus_confidence:.2%}")
        print(f"   ⚡ Processing Time: {result.processing_time:.2f}s")
        print(f"   👤 Requires Human Review: {result.requires_human_review}")
        print()
        
        if hasattr(result.final_analysis, 'claim_type'):
            print(f"   🏷️  Claim Type: {result.final_analysis.claim_type}")
        
        if hasattr(result.final_analysis, 'urgency'):
            print(f"   🚨 Urgency Level: {result.final_analysis.urgency}")
        
        print()
        print("🤖 Model Results:")
        for model_result in result.model_results:
            status = "✅" if not model_result.error else "❌"
            print(f"   {status} {model_result.model_name}: {model_result.confidence_score:.2%} confidence")
            if model_result.error:
                print(f"      Error: {model_result.error}")
        
        print()
        print("🎯 Consensus Details:")
        print(f"   Strategy: {result.consensus_strategy.value}")
        if result.disagreement_areas:
            print(f"   Disagreements: {', '.join(result.disagreement_areas)}")
        else:
            print("   No significant disagreements between models")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    print("🚀 Starting Multi-Model Classifier Test")
    print()
    
    # Check environment variables
    required_env_vars = ["OPENAI_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these variables and try again.")
        return False
    
    print("✅ Environment variables check passed")
    print()
    
    # Run the test
    success = await test_classifier()
    
    if success:
        print()
        print("🎉 Multi-Model Classifier test completed successfully!")
        print("The GPT-4o powered system is working correctly.")
    else:
        print()
        print("💥 Test failed. Please check the error messages above.")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
