#!/usr/bin/env python3
"""
🧪 Simple Supabase Test
"""

import asyncio
import os
import sys
import uuid
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.config.settings import Settings
from src.database.supabase_client import SupabaseClient

async def test_supabase():
    """Test Supabase database and storage"""
    print("🧪 Testing Supabase...")
    
    try:
        # Check environment variables
        print("\n🔧 Environment Variables:")
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_ANON_KEY')
        
        if supabase_url:
            print(f"✅ SUPABASE_URL: {supabase_url}")
        else:
            print("❌ SUPABASE_URL: Missing")
            return
            
        if supabase_key:
            print(f"✅ SUPABASE_ANON_KEY: {supabase_key[:20]}...")
        else:
            print("❌ SUPABASE_ANON_KEY: Missing")
            return
        
        # Initialize client
        settings = Settings()
        supabase_client = SupabaseClient(settings)
        
        # Test 1: Create test claim
        print("\n1️⃣ Testing claim creation...")
        test_claim_id = str(uuid.uuid4())
        
        claim_data = {
            'id': test_claim_id,
            'workflow_id': f'test_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'sender_email': '<EMAIL>',
            'subject': 'Test Claim',
            'body': 'Test claim body',
            'email_type': 'CLAIM_SUBMISSION',
            'confidence': 0.95,
            'claim_type': 'AUTO',
            'urgency_level': 'MEDIUM',
            'status': 'PENDING'
        }
        
        result = await supabase_client.create_claim(claim_data)
        print(f"✅ Claim created: {result.get('id')}")
        
        # Test 2: Create Zendesk ticket record
        print("\n2️⃣ Testing Zendesk ticket record...")
        ticket_data = {
            'claim_id': test_claim_id,
            'zendesk_ticket_id': '12345',
            'zendesk_url': 'https://test.zendesk.com/tickets/12345',
            'subject': 'Test Ticket',
            'status': 'new',
            'priority': 'normal',
            'sync_status': 'synced'
        }
        
        ticket_record = await supabase_client.create_zendesk_ticket_record(ticket_data)
        print(f"✅ Ticket record created: {ticket_record.get('id')}")
        
        # Test 3: Test file upload
        print("\n3️⃣ Testing file upload...")
        test_content = f"Test file {datetime.now()}"
        test_file = "test_upload.txt"
        
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        upload_result = await supabase_client.upload_attachment(
            file_path=test_file,
            workflow_id=claim_data['workflow_id'],
            original_filename=test_file
        )
        
        if upload_result:
            print(f"✅ File uploaded: {upload_result.get('storage_path')}")
        else:
            print("❌ File upload failed")
        
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)
        
        print("\n✅ All Supabase tests passed!")
        
    except Exception as e:
        print(f"\n❌ Supabase test failed: {str(e)}")
        print(f"Error type: {type(e).__name__}")

if __name__ == "__main__":
    asyncio.run(test_supabase())
