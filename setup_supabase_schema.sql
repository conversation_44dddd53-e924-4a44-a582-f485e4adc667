-- 🗄️ Supabase Database Schema Setup for Zurich Claims Processing
-- Run this script in your Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Claims table
CREATE TABLE IF NOT EXISTS claims (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id TEXT NOT NULL UNIQUE,
    sender_email TEXT NOT NULL,
    subject TEXT,
    body TEXT,
    email_type TEXT NOT NULL,
    confidence DECIMAL(3,2),
    claim_type TEXT,
    urgency_level TEXT,
    status TEXT DEFAULT 'PENDING',
    context_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Zendesk tickets table
CREATE TABLE IF NOT EXISTS zendesk_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID REFERENCES claims(id),
    zendesk_ticket_id TEXT NOT NULL,
    zendesk_url TEXT,
    subject TEXT,
    status TEXT,
    priority TEXT,
    sync_status TEXT DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_sync_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Attachments table
CREATE TABLE IF NOT EXISTS attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id TEXT NOT NULL,
    claim_id UUID REFERENCES claims(id),
    original_filename TEXT NOT NULL,
    storage_path TEXT NOT NULL,
    file_size BIGINT,
    content_type TEXT,
    upload_status TEXT DEFAULT 'uploaded',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Claim history table
CREATE TABLE IF NOT EXISTS claim_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID REFERENCES claims(id),
    event_type TEXT NOT NULL,
    description TEXT,
    old_values JSONB,
    new_values JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_claims_workflow_id ON claims(workflow_id);
CREATE INDEX IF NOT EXISTS idx_claims_sender_email ON claims(sender_email);
CREATE INDEX IF NOT EXISTS idx_claims_status ON claims(status);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_claim_id ON zendesk_tickets(claim_id);
CREATE INDEX IF NOT EXISTS idx_zendesk_tickets_ticket_id ON zendesk_tickets(zendesk_ticket_id);
CREATE INDEX IF NOT EXISTS idx_attachments_workflow_id ON attachments(workflow_id);
CREATE INDEX IF NOT EXISTS idx_attachments_claim_id ON attachments(claim_id);
CREATE INDEX IF NOT EXISTS idx_claim_history_claim_id ON claim_history(claim_id);

-- Enable Row Level Security (RLS)
ALTER TABLE claims ENABLE ROW LEVEL SECURITY;
ALTER TABLE zendesk_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE claim_history ENABLE ROW LEVEL SECURITY;

-- Create policies for service role access (adjust as needed)
CREATE POLICY "Enable all access for service role" ON claims
    FOR ALL USING (true);

CREATE POLICY "Enable all access for service role" ON zendesk_tickets
    FOR ALL USING (true);

CREATE POLICY "Enable all access for service role" ON attachments
    FOR ALL USING (true);

CREATE POLICY "Enable all access for service role" ON claim_history
    FOR ALL USING (true);

-- Insert a test record to verify setup
INSERT INTO claims (
    workflow_id,
    sender_email,
    subject,
    body,
    email_type,
    confidence,
    claim_type,
    urgency_level,
    status
) VALUES (
    'setup_test_' || extract(epoch from now()),
    '<EMAIL>',
    'Database Setup Test',
    'This is a test record created during database setup',
    'CLAIM_SUBMISSION',
    0.99,
    'AUTO',
    'LOW',
    'SETUP_TEST'
) ON CONFLICT (workflow_id) DO NOTHING;

-- Show table creation results
SELECT 
    'claims' as table_name, 
    count(*) as record_count 
FROM claims
UNION ALL
SELECT 
    'zendesk_tickets' as table_name, 
    count(*) as record_count 
FROM zendesk_tickets
UNION ALL
SELECT 
    'attachments' as table_name, 
    count(*) as record_count 
FROM attachments
UNION ALL
SELECT 
    'claim_history' as table_name, 
    count(*) as record_count 
FROM claim_history;
